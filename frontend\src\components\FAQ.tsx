import React, { useState } from 'react';

const FAQ: React.FC = () => {
  const [openIndex, setOpenIndex] = useState<number | null>(null);
  const [showAll, setShowAll] = useState(false);

  const faqs = [
    {
      question: "What is forex trading?",
      answer: "Forex (Foreign Exchange) trading involves buying and selling currencies in the global marketplace. It's the largest financial market in the world, with over $6 trillion traded daily. Traders profit from the fluctuations in exchange rates between different currencies."
    },
    {
      question: "How accurate are your trading signals?",
      answer: "Our trading signals have a proven accuracy rate of 95% based on historical performance. However, past performance doesn't guarantee future results. We provide detailed risk management guidelines with every signal to help protect your capital."
    },
    {
      question: "Do I need prior experience to start?",
      answer: "No prior experience is required! Our courses are designed for complete beginners. We start with the basics of forex trading and gradually progress to advanced strategies. Our step-by-step approach ensures you build a solid foundation."
    },
    {
      question: "How much money do I need to start trading?",
      answer: "You can start with as little as $100, though we recommend starting with $500-$1000 for better risk management. The key is to never risk more than you can afford to lose and to follow proper position sizing rules."
    },
    {
      question: "What makes your signals different from others?",
      answer: "Our signals are generated by experienced traders using a combination of technical analysis, fundamental analysis, and market sentiment. Each signal includes detailed entry points, stop-loss levels, take-profit targets, and risk management guidelines."
    },
    {
      question: "Can I cancel my subscription anytime?",
      answer: "Yes, you can cancel your subscription at any time. We offer a 30-day money-back guarantee for new subscribers. If you're not satisfied with our service, you can get a full refund within the first 30 days."
    },
    {
      question: "How do I receive the trading signals?",
      answer: "Trading signals are delivered through multiple channels: email notifications, SMS alerts, mobile app push notifications, and our private Discord server. You can choose your preferred method of receiving signals."
    },
    {
      question: "Do you provide customer support?",
      answer: "Yes, we provide comprehensive customer support. Starter plan members get email support, Professional plan members get priority support, and Elite plan members get 24/7 phone support plus a personal account manager."
    },
    {
      question: "What trading platforms do you support?",
      answer: "Our signals and education are compatible with all major trading platforms including MetaTrader 4, MetaTrader 5, cTrader, and most broker platforms. We provide platform-specific guidance in our courses."
    },
    {
      question: "Is there a mobile app available?",
      answer: "Yes, we have a mobile app available for both iOS and Android devices. The app provides access to signals, courses, live sessions, and community features. Professional and Elite plan members get full app access."
    }
  ];

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section id="faqs" className="py-20 bg-gray-50 dark:bg-gray-800">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Frequently Asked Questions
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-400">
            Find answers to common questions about our forex trading services and education.
          </p>
        </div>

        <div className="space-y-4">
          {faqs.slice(0, showAll ? faqs.length : 5).map((faq, index) => (
            <div key={index} className="card">
              <button
                className="w-full px-6 py-4 text-left flex justify-between items-center focus:outline-none"
                onClick={() => toggleFAQ(index)}
              >
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white pr-4">
                  {faq.question}
                </h3>
                <svg
                  className={`w-5 h-5 text-gray-500 transform transition-transform duration-200 ${
                    openIndex === index ? 'rotate-180' : ''
                  }`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              
              {openIndex === index && (
                <div className="px-6 pb-4">
                  <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                    {faq.answer}
                  </p>
                </div>
              )}
            </div>
          ))}
        </div>

        {!showAll && faqs.length > 5 && (
          <div className="mt-8 text-center">
            <button
              onClick={() => setShowAll(true)}
              className="btn-secondary px-6 py-2"
            >
              Show More Questions
            </button>
          </div>
        )}

        <div className="mt-12 text-center">
          <div className="bg-white dark:bg-gray-900 rounded-2xl p-8 shadow-lg">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Still have questions?
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              Our support team is here to help you get started on your forex trading journey.
            </p>
            <button className="btn-primary">
              Contact Support
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FAQ;
