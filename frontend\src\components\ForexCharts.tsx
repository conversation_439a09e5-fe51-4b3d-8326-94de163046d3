import React, { useState, useEffect } from 'react';
import { useScrollAnimation } from '../hooks/useScrollAnimation';

interface ChartData {
  time: string;
  price: number;
  volume: number;
}

const ForexCharts: React.FC = () => {
  const { ref: chartsRef, isVisible: chartsVisible } = useScrollAnimation();
  const [selectedPair, setSelectedPair] = useState('EUR/USD');
  const [chartData, setChartData] = useState<ChartData[]>([]);

  // Sample forex data generator
  const generateSampleData = (pair: string) => {
    const basePrice = pair === 'EUR/USD' ? 1.0850 : pair === 'GBP/USD' ? 1.2650 : 0.6750;
    const data: ChartData[] = [];
    let currentPrice = basePrice;
    
    for (let i = 0; i < 24; i++) {
      const change = (Math.random() - 0.5) * 0.01;
      currentPrice += change;
      data.push({
        time: `${i.toString().padStart(2, '0')}:00`,
        price: Number(currentPrice.toFixed(4)),
        volume: Math.floor(Math.random() * 1000000) + 500000
      });
    }
    return data;
  };

  useEffect(() => {
    setChartData(generateSampleData(selectedPair));
  }, [selectedPair]);

  const currencyPairs = [
    { pair: 'EUR/USD', change: '+0.0023', percentage: '+0.21%', color: 'text-green-600' },
    { pair: 'GBP/USD', change: '-0.0045', percentage: '-0.35%', color: 'text-red-600' },
    { pair: 'AUD/USD', change: '+0.0012', percentage: '+0.18%', color: 'text-green-600' },
    { pair: 'USD/JPY', change: '-0.15', percentage: '-0.10%', color: 'text-red-600' }
  ];

  const maxPrice = Math.max(...chartData.map(d => d.price));
  const minPrice = Math.min(...chartData.map(d => d.price));
  const priceRange = maxPrice - minPrice;

  return (
    <section id="charts" className="py-20 bg-white dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div
          ref={chartsRef}
          className={`text-center mb-16 transition-all duration-1000 ${
            chartsVisible
              ? 'opacity-100 translate-y-0'
              : 'opacity-0 translate-y-10'
          }`}
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Live Forex Market Data
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            Stay updated with real-time forex market movements and trading opportunities.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Currency Pairs Overview */}
          <div className="lg:col-span-1">
            <div className="card p-6">
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-6">
                Major Currency Pairs
              </h3>
              <div className="space-y-4">
                {currencyPairs.map((currency, index) => (
                  <div
                    key={index}
                    onClick={() => setSelectedPair(currency.pair)}
                    className={`p-4 rounded-lg cursor-pointer transition-colors ${
                      selectedPair === currency.pair
                        ? 'bg-primary-100 dark:bg-primary-900 border-primary-500'
                        : 'bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700'
                    } border`}
                  >
                    <div className="flex justify-between items-center">
                      <div>
                        <div className="font-semibold text-gray-900 dark:text-white">
                          {currency.pair}
                        </div>
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          {chartData.find(d => d.time === '23:00')?.price.toFixed(4) || '1.0850'}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className={`font-semibold ${currency.color}`}>
                          {currency.change}
                        </div>
                        <div className={`text-sm ${currency.color}`}>
                          {currency.percentage}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Chart Area */}
          <div className="lg:col-span-2">
            <div className="card p-6">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                  {selectedPair} - 24H Chart
                </h3>
                <div className="flex space-x-2">
                  <button className="px-3 py-1 text-sm bg-primary-600 text-white rounded">1H</button>
                  <button className="px-3 py-1 text-sm bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded">4H</button>
                  <button className="px-3 py-1 text-sm bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded">1D</button>
                </div>
              </div>

              {/* Simple Line Chart */}
              <div className="relative h-64 bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                <svg className="w-full h-full" viewBox="0 0 800 200">
                  {/* Grid lines */}
                  {[0, 1, 2, 3, 4].map(i => (
                    <line
                      key={i}
                      x1="0"
                      y1={i * 50}
                      x2="800"
                      y2={i * 50}
                      stroke="currentColor"
                      strokeWidth="0.5"
                      className="text-gray-300 dark:text-gray-600"
                    />
                  ))}
                  
                  {/* Price line */}
                  <polyline
                    fill="none"
                    stroke="#3B82F6"
                    strokeWidth="2"
                    points={chartData.map((point, index) => {
                      const x = (index / (chartData.length - 1)) * 800;
                      const y = 200 - ((point.price - minPrice) / priceRange) * 180;
                      return `${x},${y}`;
                    }).join(' ')}
                  />
                  
                  {/* Data points */}
                  {chartData.map((point, index) => {
                    const x = (index / (chartData.length - 1)) * 800;
                    const y = 200 - ((point.price - minPrice) / priceRange) * 180;
                    return (
                      <circle
                        key={index}
                        cx={x}
                        cy={y}
                        r="3"
                        fill="#3B82F6"
                        className="hover:r-5 transition-all"
                      />
                    );
                  })}
                </svg>
                
                {/* Price labels */}
                <div className="absolute left-0 top-0 h-full flex flex-col justify-between text-xs text-gray-600 dark:text-gray-400">
                  <span>{maxPrice.toFixed(4)}</span>
                  <span>{((maxPrice + minPrice) / 2).toFixed(4)}</span>
                  <span>{minPrice.toFixed(4)}</span>
                </div>
              </div>

              {/* Chart Stats */}
              <div className="grid grid-cols-4 gap-4 mt-6">
                <div className="text-center">
                  <div className="text-sm text-gray-600 dark:text-gray-400">High</div>
                  <div className="font-semibold text-gray-900 dark:text-white">
                    {maxPrice.toFixed(4)}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-sm text-gray-600 dark:text-gray-400">Low</div>
                  <div className="font-semibold text-gray-900 dark:text-white">
                    {minPrice.toFixed(4)}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-sm text-gray-600 dark:text-gray-400">Volume</div>
                  <div className="font-semibold text-gray-900 dark:text-white">
                    {(chartData.reduce((sum, d) => sum + d.volume, 0) / 1000000).toFixed(1)}M
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-sm text-gray-600 dark:text-gray-400">Spread</div>
                  <div className="font-semibold text-gray-900 dark:text-white">
                    0.8 pips
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Market Analysis */}
        <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="card p-6 text-center">
            <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M7 14l3-3 3 3 5-5v4h4V7h-6l5 5-3 3-3-3-5 5z"/>
              </svg>
            </div>
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              Bullish Trend
            </h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              EUR/USD showing strong upward momentum
            </p>
          </div>
          
          <div className="card p-6 text-center">
            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
            </div>
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              High Volatility
            </h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Increased trading opportunities available
            </p>
          </div>
          
          <div className="card p-6 text-center">
            <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              Signal Alert
            </h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              New trading signal available for members
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ForexCharts;
