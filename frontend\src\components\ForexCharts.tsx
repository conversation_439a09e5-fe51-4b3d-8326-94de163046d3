import React, { useState } from 'react';
import { useScrollAnimation } from '../hooks/useScrollAnimation';

const ForexCharts: React.FC = () => {
  const { ref: chartsRef, isVisible: chartsVisible } = useScrollAnimation();
  const [selectedPair, setSelectedPair] = useState('EURUSD');

  const forexPairs = [
    { symbol: 'EURUSD', name: 'EUR/USD', price: '1.0845', change: '+0.0023', changePercent: '+0.21%', trend: 'up' },
    { symbol: 'GBPUSD', name: 'GBP/USD', price: '1.2634', change: '-0.0012', changePercent: '-0.09%', trend: 'down' },
    { symbol: 'USDJPY', name: 'USD/JPY', price: '156.42', change: '+0.15', changePercent: '+0.10%', trend: 'up' },
    { symbol: 'USDCHF', name: 'USD/CHF', price: '0.8756', change: '+0.0008', changePercent: '+0.09%', trend: 'up' },
    { symbol: 'AUDUSD', name: 'AUD/USD', price: '0.6523', change: '-0.0034', changePercent: '-0.52%', trend: 'down' },
    { symbol: 'USDCAD', name: 'USD/CAD', price: '1.3845', change: '+0.0012', changePercent: '+0.09%', trend: 'up' },
  ];

  const getTradingViewSymbol = (symbol: string) => {
    return `FX:${symbol}`;
  };

  const maxPrice = Math.max(...chartData.map(d => d.price));
  const minPrice = Math.min(...chartData.map(d => d.price));
  const priceRange = maxPrice - minPrice;

  return (
    <section id="charts" className="py-20 bg-white dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div
          ref={chartsRef}
          className={`text-center mb-16 transition-all duration-1000 ${
            chartsVisible
              ? 'opacity-100 translate-y-0'
              : 'opacity-0 translate-y-10'
          }`}
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Live Forex Market Data
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            Stay updated with real-time forex market movements and trading opportunities.
          </p>
        </div>

        {/* Currency Pairs Grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-8">
          {forexPairs.map((pair) => (
            <button
              key={pair.symbol}
              onClick={() => setSelectedPair(pair.symbol)}
              className={`p-4 rounded-lg border-2 transition-all duration-300 ${
                selectedPair === pair.symbol
                  ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                  : 'border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 hover:border-primary-300'
              }`}
            >
              <div className="text-center">
                <div className="font-semibold text-gray-900 dark:text-white mb-1">
                  {pair.name}
                </div>
                <div className="text-lg font-bold text-gray-900 dark:text-white mb-1">
                  {pair.price}
                </div>
                <div className={`text-sm font-medium ${
                  pair.trend === 'up' ? 'text-green-600' : 'text-red-600'
                }`}>
                  {pair.change} ({pair.changePercent})
                </div>
                <div className="flex justify-center mt-2">
                  {pair.trend === 'up' ? (
                    <svg className="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M7 14l5-5 5 5z"/>
                    </svg>
                  ) : (
                    <svg className="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M7 10l5 5 5-5z"/>
                    </svg>
                  )}
                </div>
              </div>
            </button>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Chart */}
          <div className="lg:col-span-2">

            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-2xl font-semibold text-gray-900 dark:text-white">
                  {forexPairs.find(p => p.symbol === selectedPair)?.name} Live Chart
                </h3>
                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <div className="text-2xl font-bold text-gray-900 dark:text-white">
                      {forexPairs.find(p => p.symbol === selectedPair)?.price}
                    </div>
                    <div className={`text-sm font-medium ${
                      forexPairs.find(p => p.symbol === selectedPair)?.trend === 'up'
                        ? 'text-green-600'
                        : 'text-red-600'
                    }`}>
                      {forexPairs.find(p => p.symbol === selectedPair)?.change} (
                      {forexPairs.find(p => p.symbol === selectedPair)?.changePercent})
                    </div>
                  </div>
                </div>
              </div>

              {/* TradingView Chart */}
              <div className="aspect-video bg-gray-50 dark:bg-gray-700 rounded-lg">
                <iframe
                  key={selectedPair}
                  src={`https://s.tradingview.com/widgetembed/?frameElementId=tradingview_${selectedPair}&symbol=${getTradingViewSymbol(selectedPair)}&interval=1H&hidesidetoolbar=0&hidetoptoolbar=0&symboledit=1&saveimage=1&toolbarbg=F1F3F6&studies=%5B%5D&hideideas=1&theme=Light&style=1&timezone=Etc%2FUTC&studies_overrides=%7B%7D&overrides=%7B%7D&enabled_features=%5B%5D&disabled_features=%5B%5D&locale=en&utm_source=localhost&utm_medium=widget&utm_campaign=chart&utm_term=${getTradingViewSymbol(selectedPair)}`}
                  className="w-full h-full rounded-lg"
                  frameBorder="0"
                  allowTransparency={true}
                  scrolling="no"
                  allowFullScreen={true}
                ></iframe>
              </div>
            </div>
          </div>

          {/* Market Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-6">
                Market Summary
              </h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <div>
                    <div className="font-semibold text-gray-900 dark:text-white">Market Sentiment</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">Overall trend</div>
                  </div>
                  <div className="text-green-600 font-bold">Bullish</div>
                </div>

                <div className="flex items-center justify-between p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                  <div>
                    <div className="font-semibold text-gray-900 dark:text-white">Volatility</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">Price movement</div>
                  </div>
                  <div className="text-yellow-600 font-bold">Medium</div>
                </div>

                <div className="flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <div>
                    <div className="font-semibold text-gray-900 dark:text-white">Volume</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">Trading activity</div>
                  </div>
                  <div className="text-blue-600 font-bold">High</div>
                </div>

                <div className="flex items-center justify-between p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                  <div>
                    <div className="font-semibold text-gray-900 dark:text-white">Next Update</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">Data refresh</div>
                  </div>
                  <div className="text-purple-600 font-bold">15 min</div>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="mt-6 space-y-3">
                <button className="w-full bg-primary-600 text-white py-2 px-4 rounded-lg hover:bg-primary-700 transition-colors">
                  Get Trading Signals
                </button>
                <button className="w-full border border-primary-600 text-primary-600 py-2 px-4 rounded-lg hover:bg-primary-50 dark:hover:bg-primary-900/20 transition-colors">
                  Market Analysis
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Market Analysis */}
        <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="card p-6 text-center">
            <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M7 14l3-3 3 3 5-5v4h4V7h-6l5 5-3 3-3-3-5 5z"/>
              </svg>
            </div>
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              Bullish Trend
            </h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              EUR/USD showing strong upward momentum
            </p>
          </div>
          
          <div className="card p-6 text-center">
            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
            </div>
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              High Volatility
            </h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Increased trading opportunities available
            </p>
          </div>
          
          <div className="card p-6 text-center">
            <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              Signal Alert
            </h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              New trading signal available for members
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ForexCharts;
