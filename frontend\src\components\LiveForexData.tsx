import React, { useState } from 'react';
import { useScrollAnimation } from '../hooks/useScrollAnimation';

const LiveForexData: React.FC = () => {
  const { ref: sectionRef, isVisible: sectionVisible } = useScrollAnimation();
  const [selectedPair, setSelectedPair] = useState('EURUSD');

  const forexPairs = [
    { symbol: 'EURUSD', name: 'EUR/USD', price: '1.0845', change: '+0.0023', changePercent: '+0.21%', trend: 'up' },
    { symbol: 'GBPUSD', name: 'GBP/USD', price: '1.2634', change: '-0.0012', changePercent: '-0.09%', trend: 'down' },
    { symbol: 'USDJPY', name: 'USD/JPY', price: '156.42', change: '+0.15', changePercent: '+0.10%', trend: 'up' },
    { symbol: 'USDCHF', name: 'USD/CHF', price: '0.8756', change: '+0.0008', changePercent: '+0.09%', trend: 'up' },
    { symbol: 'AUDUSD', name: 'AUD/USD', price: '0.6523', change: '-0.0034', changePercent: '-0.52%', trend: 'down' },
    { symbol: 'USDCAD', name: 'USD/CAD', price: '1.3845', change: '+0.0012', changePercent: '+0.09%', trend: 'up' },
  ];

  const getTradingViewSymbol = (symbol: string) => {
    return `FX:${symbol}`;
  };

  return (
    <section className="py-20 bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div
          ref={sectionRef}
          className={`transition-all duration-1000 ${
            sectionVisible
              ? 'opacity-100 translate-y-0'
              : 'opacity-0 translate-y-10'
          }`}
        >
          {/* Section Header */}
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Live Forex Market Data
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              Real-time forex rates and interactive charts powered by professional trading data. 
              Monitor major currency pairs and make informed trading decisions.
            </p>
          </div>

          {/* Currency Pairs Grid */}
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-8">
            {forexPairs.map((pair) => (
              <button
                key={pair.symbol}
                onClick={() => setSelectedPair(pair.symbol)}
                className={`p-4 rounded-lg border-2 transition-all duration-300 ${
                  selectedPair === pair.symbol
                    ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                    : 'border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 hover:border-primary-300'
                }`}
              >
                <div className="text-center">
                  <div className="font-semibold text-gray-900 dark:text-white mb-1">
                    {pair.name}
                  </div>
                  <div className="text-lg font-bold text-gray-900 dark:text-white mb-1">
                    {pair.price}
                  </div>
                  <div className={`text-sm font-medium ${
                    pair.trend === 'up' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {pair.change} ({pair.changePercent})
                  </div>
                  <div className="flex justify-center mt-2">
                    {pair.trend === 'up' ? (
                      <svg className="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M7 14l5-5 5 5z"/>
                      </svg>
                    ) : (
                      <svg className="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M7 10l5 5 5-5z"/>
                      </svg>
                    )}
                  </div>
                </div>
              </button>
            ))}
          </div>

          {/* Main Chart */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-2xl font-semibold text-gray-900 dark:text-white">
                {forexPairs.find(p => p.symbol === selectedPair)?.name} Live Chart
              </h3>
              <div className="flex items-center space-x-4">
                <div className="text-right">
                  <div className="text-2xl font-bold text-gray-900 dark:text-white">
                    {forexPairs.find(p => p.symbol === selectedPair)?.price}
                  </div>
                  <div className={`text-sm font-medium ${
                    forexPairs.find(p => p.symbol === selectedPair)?.trend === 'up' 
                      ? 'text-green-600' 
                      : 'text-red-600'
                  }`}>
                    {forexPairs.find(p => p.symbol === selectedPair)?.change} (
                    {forexPairs.find(p => p.symbol === selectedPair)?.changePercent})
                  </div>
                </div>
              </div>
            </div>
            
            {/* TradingView Chart */}
            <div className="aspect-video bg-gray-50 dark:bg-gray-700 rounded-lg">
              <iframe
                key={selectedPair}
                src={`https://s.tradingview.com/widgetembed/?frameElementId=tradingview_${selectedPair}&symbol=${getTradingViewSymbol(selectedPair)}&interval=1H&hidesidetoolbar=0&hidetoptoolbar=0&symboledit=1&saveimage=1&toolbarbg=F1F3F6&studies=%5B%5D&hideideas=1&theme=Light&style=1&timezone=Etc%2FUTC&studies_overrides=%7B%7D&overrides=%7B%7D&enabled_features=%5B%5D&disabled_features=%5B%5D&locale=en&utm_source=localhost&utm_medium=widget&utm_campaign=chart&utm_term=${getTradingViewSymbol(selectedPair)}`}
                className="w-full h-full rounded-lg"
                frameBorder="0"
                allowTransparency={true}
                scrolling="no"
                allowFullScreen={true}
              ></iframe>
            </div>
          </div>

          {/* Market Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    Market Sentiment
                  </h4>
                  <div className="text-2xl font-bold text-green-600">Bullish</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Based on major pairs analysis
                  </div>
                </div>
                <div className="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                  <svg className="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M7 14l5-5 5 5z"/>
                  </svg>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    Volatility Index
                  </h4>
                  <div className="text-2xl font-bold text-yellow-600">Medium</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Moderate price movements
                  </div>
                </div>
                <div className="w-12 h-12 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg flex items-center justify-center">
                  <svg className="w-6 h-6 text-yellow-600" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    Trading Volume
                  </h4>
                  <div className="text-2xl font-bold text-blue-600">High</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Above average activity
                  </div>
                </div>
                <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                  <svg className="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M3 13h8V3H9v6H3v4zm0 8h6v-2H3v2zm10 0h8v-6h-8v6zm5-12V3h-5v6h5z"/>
                  </svg>
                </div>
              </div>
            </div>
          </div>

          {/* Call to Action */}
          <div className="text-center">
            <div className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-lg p-8 text-white">
              <h3 className="text-2xl font-bold mb-4">
                Ready to Start Trading?
              </h3>
              <p className="text-lg mb-6 opacity-90">
                Join thousands of successful traders using our platform for real-time signals and expert guidance.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button className="bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                  Get Trading Signals
                </button>
                <button className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary-600 transition-colors">
                  Start Free Trial
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default LiveForexData;
