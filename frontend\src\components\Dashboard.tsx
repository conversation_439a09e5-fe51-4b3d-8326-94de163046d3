import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext';
import ETRIBChatbot from './ETRIBChatbot';

interface Booking {
  id: string;
  date: string;
  time: string;
  sessionType: string;
  duration: string;
  status: 'pending' | 'approved' | 'rejected';
  notes?: string;
  createdAt: string;
}

interface Course {
  id: string;
  title: string;
  description: string;
  price: number;
  duration: string;
  level: 'Beginner' | 'Intermediate' | 'Advanced';
  modules: number;
  lessons: number;
  thumbnail: string;
  instructor: string;
  rating: number;
  students: number;
  isEnrolled: boolean;
  isPaid: boolean;
  progress?: number;
  category: string;
}

interface Lesson {
  id: string;
  title: string;
  description: string;
  duration: string;
  isCompleted: boolean;
  progress: number;
  videoUrl?: string;
  pdfUrl?: string;
  isLocked: boolean;
}

const Dashboard: React.FC = () => {
  const { user, logout } = useAuth();
  const { isDark, toggleTheme } = useTheme();
  const [activeSection, setActiveSection] = useState('dashboard');

  // Course data
  const [courses, setCourses] = useState<Course[]>([
    {
      id: '1',
      title: 'Full GOAT Strategy Course',
      description: 'Master the complete GOAT trading strategy with advanced techniques',
      price: 299,
      duration: '8 weeks',
      level: 'Advanced',
      modules: 4,
      lessons: 12,
      thumbnail: '/api/placeholder/300/200',
      instructor: 'Ettivr',
      rating: 4.9,
      students: 1250,
      isEnrolled: true,
      isPaid: true,
      progress: 58,
      category: 'Strategy'
    },
    {
      id: '2',
      title: 'Forex Fundamentals',
      description: 'Complete beginner course covering all forex basics',
      price: 149,
      duration: '4 weeks',
      level: 'Beginner',
      modules: 3,
      lessons: 8,
      thumbnail: '/api/placeholder/300/200',
      instructor: 'Ettivr',
      rating: 4.8,
      students: 2100,
      isEnrolled: true,
      isPaid: false,
      progress: 0,
      category: 'Fundamentals'
    },
    {
      id: '3',
      title: 'Technical Analysis Mastery',
      description: 'Advanced technical analysis techniques and chart patterns',
      price: 199,
      duration: '6 weeks',
      level: 'Intermediate',
      modules: 5,
      lessons: 15,
      thumbnail: '/api/placeholder/300/200',
      instructor: 'Ettivr',
      rating: 4.9,
      students: 890,
      isEnrolled: false,
      isPaid: false,
      progress: 0,
      category: 'Technical Analysis'
    },
    {
      id: '4',
      title: 'Risk Management Pro',
      description: 'Professional risk management strategies for consistent profits',
      price: 179,
      duration: '3 weeks',
      level: 'Intermediate',
      modules: 3,
      lessons: 9,
      thumbnail: '/api/placeholder/300/200',
      instructor: 'Ettivr',
      rating: 4.7,
      students: 650,
      isEnrolled: false,
      isPaid: false,
      progress: 0,
      category: 'Risk Management'
    },
    {
      id: '5',
      title: 'Psychology of Trading',
      description: 'Master your trading mindset and emotional control',
      price: 129,
      duration: '2 weeks',
      level: 'Beginner',
      modules: 2,
      lessons: 6,
      thumbnail: '/api/placeholder/300/200',
      instructor: 'Ettivr',
      rating: 4.6,
      students: 1800,
      isEnrolled: false,
      isPaid: false,
      progress: 0,
      category: 'Psychology'
    }
  ]);

  // Booking state
  const [bookings, setBookings] = useState<Booking[]>([
    {
      id: '1',
      date: '2024-01-15',
      time: '14:00',
      sessionType: 'One-on-One Coaching',
      duration: '60 minutes',
      status: 'approved',
      notes: 'Focus on risk management strategies',
      createdAt: '2024-01-10'
    },
    {
      id: '2',
      date: '2024-01-20',
      time: '10:00',
      sessionType: 'Strategy Review',
      duration: '30 minutes',
      status: 'pending',
      notes: 'Review current trading performance',
      createdAt: '2024-01-12'
    },
    {
      id: '3',
      date: '2024-01-08',
      time: '16:00',
      sessionType: 'Technical Analysis Session',
      duration: '45 minutes',
      status: 'rejected',
      notes: 'Requested time slot not available',
      createdAt: '2024-01-05'
    }
  ]);

  const [newBooking, setNewBooking] = useState({
    date: '',
    time: '',
    sessionType: '',
    duration: '',
    notes: ''
  });

  // Course management functions
  const handleEnrollCourse = (courseId: string) => {
    setCourses(courses.map(course =>
      course.id === courseId
        ? { ...course, isEnrolled: true }
        : course
    ));
  };

  const handlePayForCourse = (courseId: string) => {
    setCourses(courses.map(course =>
      course.id === courseId
        ? { ...course, isPaid: true }
        : course
    ));
  };

  const menuItems = [
    { id: 'dashboard', name: 'Dashboard', icon: '📊' },
    { id: 'signals', name: 'Signals', icon: '📈', description: 'Premium trading signals' },
    { id: 'course', name: 'Course', icon: '📚', description: 'Full GOAT strategy' },
    { id: 'one-on-one', name: 'One on One', icon: '👥', description: 'Personal coaching' },
    { id: 'trade-with-ettivr', name: 'Trade With Ettivr', icon: '🎯', description: 'Live trading sessions' },
    { id: 'collaborations', name: 'Collaborations', icon: '🤝', description: 'Brand partnerships' },
    { id: 'academy', name: 'Academy', icon: '🎓', description: 'Beginner training' },
    { id: 'booking', name: 'Booking', icon: '📅', description: 'Schedule sessions' },
    { id: 'enquiry', name: 'Enquiry', icon: '❓', description: 'Get in touch' },
    { id: 'settings', name: 'Settings', icon: '⚙️', description: 'Account & preferences' }
  ];

  const renderContent = () => {
    switch (activeSection) {
      case 'signals':
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-dark-900 dark:text-white">Premium Trading Signals</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[1, 2, 3].map((signal) => (
                <div key={signal} className="card p-6">
                  <div className="flex items-center justify-between mb-4">
                    <span className="text-lg font-semibold text-gray-900 dark:text-white">EUR/USD</span>
                    <span className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 px-2 py-1 rounded-full text-sm">BUY</span>
                  </div>
                  <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                    <div className="flex justify-between">
                      <span>Entry:</span>
                      <span className="font-medium">1.0850</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Stop Loss:</span>
                      <span className="font-medium">1.0820</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Take Profit:</span>
                      <span className="font-medium">1.0920</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        );
      case 'course':
        const activeCourses = courses.filter(course => course.isEnrolled && course.isPaid);
        const unpaidCourses = courses.filter(course => course.isEnrolled && !course.isPaid);

        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">My Courses</h2>
            </div>

            {/* Active Courses */}
            {activeCourses.length > 0 && (
              <div className="space-y-4">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Active Courses</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {activeCourses.map((course) => (
                    <div key={course.id} className="card p-6">
                      <div className="flex items-center justify-between mb-4">
                        <h4 className="text-lg font-semibold text-gray-900 dark:text-white">{course.title}</h4>
                        <span className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 px-3 py-1 rounded-full text-sm font-medium">
                          Active
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">{course.description}</p>
                      <div className="space-y-3">
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600 dark:text-gray-400">Progress</span>
                          <span className="text-primary-600 font-semibold">{course.progress}%</span>
                        </div>
                        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                          <div
                            className="bg-gradient-to-r from-primary-500 to-primary-600 h-2 rounded-full"
                            style={{width: `${course.progress}%`}}
                          ></div>
                        </div>
                        <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
                          <span>{course.modules} modules • {course.lessons} lessons</span>
                          <span>{course.duration}</span>
                        </div>
                        <button className="w-full bg-primary-600 text-white py-2 px-4 rounded-lg hover:bg-primary-700 transition-colors">
                          Continue Learning
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Unpaid Courses */}
            {unpaidCourses.length > 0 && (
              <div className="space-y-4">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Complete Your Enrollment</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {unpaidCourses.map((course) => (
                    <div key={course.id} className="card p-6 border-2 border-yellow-200 dark:border-yellow-800">
                      <div className="flex items-center justify-between mb-4">
                        <h4 className="text-lg font-semibold text-gray-900 dark:text-white">{course.title}</h4>
                        <span className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 px-3 py-1 rounded-full text-sm font-medium">
                          Payment Required
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">{course.description}</p>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
                          <span>{course.modules} modules • {course.lessons} lessons</span>
                          <span>{course.duration}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-2xl font-bold text-gray-900 dark:text-white">${course.price}</span>
                          <span className="text-sm text-gray-600 dark:text-gray-400">{course.level}</span>
                        </div>
                        <button
                          onClick={() => handlePayForCourse(course.id)}
                          className="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors"
                        >
                          Complete Payment
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* No courses message */}
            {activeCourses.length === 0 && unpaidCourses.length === 0 && (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">📚</div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">No Enrolled Courses</h3>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  Explore our academy to find courses that match your learning goals.
                </p>
                <button
                  onClick={() => setActiveSection('academy')}
                  className="bg-primary-600 text-white py-2 px-6 rounded-lg hover:bg-primary-700 transition-colors"
                >
                  Browse Academy
                </button>
              </div>
            )}
          </div>
        );
      case 'academy':
        const availableCourses = courses.filter(course => !course.isEnrolled);
        const categories = [...new Set(courses.map(course => course.category))];

        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Academy</h2>
              <p className="text-gray-600 dark:text-gray-400">Discover and enroll in our comprehensive trading courses</p>
            </div>

            {/* Course Categories Filter */}
            <div className="flex flex-wrap gap-2">
              <button className="px-4 py-2 bg-primary-600 text-white rounded-lg text-sm font-medium">
                All Courses
              </button>
              {categories.map((category) => (
                <button
                  key={category}
                  className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg text-sm font-medium hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
                >
                  {category}
                </button>
              ))}
            </div>

            {/* Available Courses Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {courses.map((course) => (
                <div key={course.id} className="card p-6 hover:shadow-lg transition-shadow">
                  <div className="aspect-video bg-gray-200 dark:bg-gray-700 rounded-lg mb-4 flex items-center justify-center">
                    <svg className="w-12 h-12 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M8 5v14l11-7z"/>
                    </svg>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-start justify-between">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{course.title}</h3>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        course.level === 'Beginner' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                        course.level === 'Intermediate' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                        'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                      }`}>
                        {course.level}
                      </span>
                    </div>

                    <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">{course.description}</p>

                    <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
                      <div className="flex items-center space-x-1">
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                        <span>{course.rating}</span>
                      </div>
                      <span>•</span>
                      <span>{course.students} students</span>
                    </div>

                    <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
                      <span>{course.modules} modules • {course.lessons} lessons</span>
                      <span>{course.duration}</span>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-2xl font-bold text-gray-900 dark:text-white">${course.price}</span>
                      <span className="text-sm text-gray-600 dark:text-gray-400">by {course.instructor}</span>
                    </div>

                    {course.isEnrolled ? (
                      course.isPaid ? (
                        <button
                          onClick={() => setActiveSection('course')}
                          className="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors"
                        >
                          Continue Learning
                        </button>
                      ) : (
                        <button
                          onClick={() => handlePayForCourse(course.id)}
                          className="w-full bg-yellow-600 text-white py-2 px-4 rounded-lg hover:bg-yellow-700 transition-colors"
                        >
                          Complete Payment
                        </button>
                      )
                    ) : (
                      <button
                        onClick={() => handleEnrollCourse(course.id)}
                        className="w-full bg-primary-600 text-white py-2 px-4 rounded-lg hover:bg-primary-700 transition-colors"
                      >
                        Enroll Now
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {/* No courses message */}
            {availableCourses.length === 0 && (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">🎓</div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">All Courses Enrolled</h3>
                <p className="text-gray-600 dark:text-gray-400">
                  You're enrolled in all available courses. Check back later for new content!
                </p>
              </div>
            )}
          </div>
        );
      case 'settings':
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Settings</h2>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Account Settings */}
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Account Settings</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Full Name
                    </label>
                    <input
                      type="text"
                      defaultValue={user?.name || 'Chris Mburu'}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Email Address
                    </label>
                    <input
                      type="email"
                      defaultValue={user?.email || '<EMAIL>'}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Phone Number
                    </label>
                    <input
                      type="tel"
                      placeholder="+****************"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>
                  <button className="btn-primary w-full">Update Account</button>
                </div>
              </div>

              {/* Preferences */}
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Preferences</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Dark Mode
                      </label>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Toggle between light and dark theme
                      </p>
                    </div>
                    <button
                      onClick={toggleTheme}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                        isDark ? 'bg-primary-600' : 'bg-gray-200'
                      }`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                          isDark ? 'translate-x-6' : 'translate-x-1'
                        }`}
                      />
                    </button>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Email Notifications
                      </label>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Receive trading signals via email
                      </p>
                    </div>
                    <button className="relative inline-flex h-6 w-11 items-center rounded-full bg-primary-600">
                      <span className="inline-block h-4 w-4 transform rounded-full bg-white translate-x-6" />
                    </button>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        SMS Alerts
                      </label>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Get urgent signals via SMS
                      </p>
                    </div>
                    <button className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200">
                      <span className="inline-block h-4 w-4 transform rounded-full bg-white translate-x-1" />
                    </button>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Trading Experience Level
                    </label>
                    <select className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                      <option>Beginner</option>
                      <option>Intermediate</option>
                      <option>Advanced</option>
                      <option>Professional</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>

            {/* Security Settings */}
            <div className="card p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Security</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Current Password
                    </label>
                    <input
                      type="password"
                      placeholder="Enter current password"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      New Password
                    </label>
                    <input
                      type="password"
                      placeholder="Enter new password"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Confirm New Password
                    </label>
                    <input
                      type="password"
                      placeholder="Confirm new password"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>
                  <button className="btn-primary w-full">Change Password</button>
                </div>

                <div className="space-y-4">
                  <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                    <h4 className="text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-2">
                      Two-Factor Authentication
                    </h4>
                    <p className="text-xs text-yellow-700 dark:text-yellow-300 mb-3">
                      Add an extra layer of security to your account
                    </p>
                    <button className="btn-secondary text-sm">Enable 2FA</button>
                  </div>

                  <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                    <h4 className="text-sm font-medium text-red-800 dark:text-red-200 mb-2">
                      Danger Zone
                    </h4>
                    <p className="text-xs text-red-700 dark:text-red-300 mb-3">
                      Permanently delete your account and all data
                    </p>
                    <button className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm transition-colors">
                      Delete Account
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      case 'booking':
        const handleBookingSubmit = (e: React.FormEvent) => {
          e.preventDefault();
          const booking: Booking = {
            id: Date.now().toString(),
            ...newBooking,
            status: 'pending',
            createdAt: new Date().toISOString().split('T')[0]
          };
          setBookings([booking, ...bookings]);
          setNewBooking({
            date: '',
            time: '',
            sessionType: '',
            duration: '',
            notes: ''
          });
        };

        const getStatusColor = (status: string) => {
          switch (status) {
            case 'approved': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
            case 'rejected': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
            default: return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
          }
        };

        const getStatusIcon = (status: string) => {
          switch (status) {
            case 'approved':
              return (
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              );
            case 'rejected':
              return (
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              );
            default:
              return (
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                </svg>
              );
          }
        };

        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Book a Session</h2>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Schedule your personal coaching session
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Booking Form */}
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Schedule New Session
                </h3>
                <form onSubmit={handleBookingSubmit} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Date *
                      </label>
                      <input
                        type="date"
                        value={newBooking.date}
                        onChange={(e) => setNewBooking({...newBooking, date: e.target.value})}
                        min={new Date().toISOString().split('T')[0]}
                        required
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Time *
                      </label>
                      <select
                        value={newBooking.time}
                        onChange={(e) => setNewBooking({...newBooking, time: e.target.value})}
                        required
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      >
                        <option value="">Select time</option>
                        <option value="09:00">9:00 AM</option>
                        <option value="10:00">10:00 AM</option>
                        <option value="11:00">11:00 AM</option>
                        <option value="14:00">2:00 PM</option>
                        <option value="15:00">3:00 PM</option>
                        <option value="16:00">4:00 PM</option>
                        <option value="17:00">5:00 PM</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Session Type *
                    </label>
                    <select
                      value={newBooking.sessionType}
                      onChange={(e) => setNewBooking({...newBooking, sessionType: e.target.value})}
                      required
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="">Select session type</option>
                      <option value="One-on-One Coaching">One-on-One Coaching</option>
                      <option value="Strategy Review">Strategy Review</option>
                      <option value="Technical Analysis Session">Technical Analysis Session</option>
                      <option value="Risk Management Consultation">Risk Management Consultation</option>
                      <option value="Portfolio Review">Portfolio Review</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Duration *
                    </label>
                    <select
                      value={newBooking.duration}
                      onChange={(e) => setNewBooking({...newBooking, duration: e.target.value})}
                      required
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="">Select duration</option>
                      <option value="30 minutes">30 minutes</option>
                      <option value="45 minutes">45 minutes</option>
                      <option value="60 minutes">60 minutes</option>
                      <option value="90 minutes">90 minutes</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Notes (Optional)
                    </label>
                    <textarea
                      value={newBooking.notes}
                      onChange={(e) => setNewBooking({...newBooking, notes: e.target.value})}
                      rows={3}
                      placeholder="Any specific topics you'd like to discuss..."
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>

                  <button
                    type="submit"
                    className="w-full btn-primary"
                  >
                    Submit Booking Request
                  </button>
                </form>
              </div>

              {/* Available Time Slots */}
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Available Time Slots
                </h3>
                <div className="space-y-3">
                  <div className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                    All times are in your local timezone
                  </div>

                  {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'].map((day) => (
                    <div key={day} className="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-700">
                      <span className="font-medium text-gray-900 dark:text-white">{day}</span>
                      <span className="text-sm text-gray-600 dark:text-gray-400">9:00 AM - 5:00 PM</span>
                    </div>
                  ))}

                  <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-1">
                      Booking Guidelines
                    </h4>
                    <ul className="text-xs text-blue-700 dark:text-blue-300 space-y-1">
                      <li>• Book at least 24 hours in advance</li>
                      <li>• Sessions can be rescheduled up to 2 hours before</li>
                      <li>• You'll receive confirmation via email</li>
                      <li>• Meeting link will be provided upon approval</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            {/* Booking History */}
            <div className="card p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Your Booking History
              </h3>
              <div className="space-y-4">
                {bookings.map((booking) => (
                  <div key={booking.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h4 className="font-semibold text-gray-900 dark:text-white">
                            {booking.sessionType}
                          </h4>
                          <span className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(booking.status)}`}>
                            {getStatusIcon(booking.status)}
                            <span className="capitalize">{booking.status}</span>
                          </span>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 dark:text-gray-400">
                          <div className="flex items-center space-x-2">
                            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                            </svg>
                            <span>{new Date(booking.date).toLocaleDateString()}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                            </svg>
                            <span>{booking.time} ({booking.duration})</span>
                          </div>
                          <div className="text-xs text-gray-500">
                            Requested: {new Date(booking.createdAt).toLocaleDateString()}
                          </div>
                        </div>
                        {booking.notes && (
                          <div className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                            <span className="font-medium">Notes:</span> {booking.notes}
                          </div>
                        )}
                      </div>
                      {booking.status === 'approved' && (
                        <button className="ml-4 bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm transition-colors">
                          Join Session
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );
      default:
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                Welcome back, {user?.name}! 👋
              </h2>
            </div>
            <p className="text-gray-600 dark:text-gray-400">
              Here's your complete navigation hub. Click any card to access that section.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {menuItems.slice(1).map((item) => (
                <div
                  key={item.id}
                  onClick={() => setActiveSection(item.id)}
                  className="card p-6 cursor-pointer hover:shadow-lg transition-shadow duration-200"
                >
                  <div className="text-3xl mb-4">{item.icon}</div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    {item.name}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {item.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex">
      {/* Sidebar */}
      <div className="w-64 bg-white dark:bg-gray-800 shadow-lg">
        <div className="p-6">
          <div className="flex items-center space-x-3 mb-8">
            <div className="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
              </svg>
            </div>
            <div>
              <h1 className="text-lg font-bold text-gray-900 dark:text-white">E TRIBE CONCEPTS</h1>
            </div>
          </div>

          <nav className="space-y-2">
            {menuItems.map((item) => (
              <button
                key={item.id}
                onClick={() => setActiveSection(item.id)}
                className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors ${
                  activeSection === item.id
                    ? 'bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
              >
                <span className="text-xl">{item.icon}</span>
                <span className="font-medium">{item.name}</span>
              </button>
            ))}
          </nav>
        </div>

        <div className="absolute bottom-0 left-0 right-0 w-64 p-6 border-t border-gray-200 dark:border-gray-700">
          <div className="bg-primary-50 dark:bg-primary-900 rounded-lg p-4">
            <h4 className="font-semibold text-primary-900 dark:text-primary-100 mb-2">Need Help?</h4>
            <p className="text-sm text-primary-700 dark:text-primary-300 mb-3">
              Contact our support team for assistance.
            </p>
            <button className="text-sm bg-primary-600 text-white px-3 py-1 rounded">
              Contact Support
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Top Bar */}
        <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 px-6 py-3">
          <div className="flex items-center justify-between">
            {/* Left side - Empty or can add other elements */}
            <div className="flex items-center space-x-4">
              <h1 className="text-xl font-semibold text-gray-900 dark:text-white capitalize">
                {activeSection.replace('-', ' ')}
              </h1>
            </div>

            {/* Right side - Forecast, Settings, Notifications, User Profile */}
            <div className="flex items-center space-x-3">
              {/* Forecast Button */}
              <button className="flex items-center space-x-2 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M3 13h8V3H9v6H3v4zm0 8h6v-2H3v2zm10 0h8v-6h-8v6zm5-12V3h-5v6h5z"/>
                </svg>
                <span className="font-medium">Forecast</span>
                <span className="bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full">NEW</span>
              </button>
              {/* Settings */}
              <button
                onClick={toggleTheme}
                className="p-2 rounded-lg text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                title="Settings"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </button>

              {/* Notifications */}
              <button className="p-2 rounded-lg text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors relative" title="Notifications">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                </svg>
                {/* Notification dot */}
                <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
              </button>

              {/* User Profile Section */}
              <div className="flex items-center space-x-3 pl-3 border-l border-gray-200 dark:border-gray-600">
                <div className="text-right">
                  <div className="text-sm font-medium text-gray-900 dark:text-white">
                    {user?.name || 'Chris Mburu'}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {user?.email || '<EMAIL>'}
                  </div>
                </div>
                <div className="relative">
                  <button className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center text-white font-medium hover:bg-red-600 transition-colors">
                    <span className="text-sm">
                      {user?.name?.charAt(0).toUpperCase() || 'C'}
                    </span>
                  </button>
                </div>
                <button
                  onClick={logout}
                  className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                  title="Logout"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </header>

        {/* Content */}
        <main className="flex-1 p-6">
          {renderContent()}
        </main>
      </div>

      {/* E-TRIB Chatbot */}
      <ETRIBChatbot />
    </div>
  );
};

export default Dashboard;
