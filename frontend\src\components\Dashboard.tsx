import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext';
import ETRIBChatbot from './ETRIBChatbot';

interface Booking {
  id: string;
  date: string;
  time: string;
  sessionType: string;
  duration: string;
  status: 'pending' | 'approved' | 'rejected';
  notes?: string;
  createdAt: string;
}

interface Course {
  id: string;
  title: string;
  description: string;
  price: number;
  duration: string;
  level: 'Beginner' | 'Intermediate' | 'Advanced';
  modules: number;
  lessons: number;
  thumbnail: string;
  instructor: string;
  rating: number;
  students: number;
  isEnrolled: boolean;
  isPaid: boolean;
  progress?: number;
  category: string;
}

interface Lesson {
  id: string;
  title: string;
  description: string;
  duration: string;
  isCompleted: boolean;
  progress: number;
  videoUrl?: string;
  pdfUrl?: string;
  isLocked: boolean;
}

interface Coach {
  id: string;
  name: string;
  title: string;
  experience: string;
  rating: number;
  totalSessions: number;
  specialties: string[];
  avatar: string;
  hourlyRate: number;
  availability: string[];
  bio: string;
}

interface CoachingSession {
  id: string;
  coachId: string;
  date: string;
  time: string;
  duration: number;
  type: string;
  status: 'scheduled' | 'completed' | 'cancelled';
  notes?: string;
  meetingLink?: string;
  price: number;
}

const Dashboard: React.FC = () => {
  const { user, logout } = useAuth();
  const { isDark, toggleTheme } = useTheme();
  const [activeSection, setActiveSection] = useState('dashboard');

  // Course data
  const [courses, setCourses] = useState<Course[]>([
    {
      id: '1',
      title: 'Full GOAT Strategy Course',
      description: 'Master the complete GOAT trading strategy with advanced techniques',
      price: 299,
      duration: '8 weeks',
      level: 'Advanced',
      modules: 4,
      lessons: 12,
      thumbnail: '/api/placeholder/300/200',
      instructor: 'Ettivr',
      rating: 4.9,
      students: 1250,
      isEnrolled: true,
      isPaid: true,
      progress: 58,
      category: 'Strategy'
    },
    {
      id: '2',
      title: 'Forex Fundamentals',
      description: 'Complete beginner course covering all forex basics',
      price: 149,
      duration: '4 weeks',
      level: 'Beginner',
      modules: 3,
      lessons: 8,
      thumbnail: '/api/placeholder/300/200',
      instructor: 'Ettivr',
      rating: 4.8,
      students: 2100,
      isEnrolled: true,
      isPaid: false,
      progress: 0,
      category: 'Fundamentals'
    },
    {
      id: '3',
      title: 'Technical Analysis Mastery',
      description: 'Advanced technical analysis techniques and chart patterns',
      price: 199,
      duration: '6 weeks',
      level: 'Intermediate',
      modules: 5,
      lessons: 15,
      thumbnail: '/api/placeholder/300/200',
      instructor: 'Ettivr',
      rating: 4.9,
      students: 890,
      isEnrolled: false,
      isPaid: false,
      progress: 0,
      category: 'Technical Analysis'
    },
    {
      id: '4',
      title: 'Risk Management Pro',
      description: 'Professional risk management strategies for consistent profits',
      price: 179,
      duration: '3 weeks',
      level: 'Intermediate',
      modules: 3,
      lessons: 9,
      thumbnail: '/api/placeholder/300/200',
      instructor: 'Ettivr',
      rating: 4.7,
      students: 650,
      isEnrolled: false,
      isPaid: false,
      progress: 0,
      category: 'Risk Management'
    },
    {
      id: '5',
      title: 'Psychology of Trading',
      description: 'Master your trading mindset and emotional control',
      price: 129,
      duration: '2 weeks',
      level: 'Beginner',
      modules: 2,
      lessons: 6,
      thumbnail: '/api/placeholder/300/200',
      instructor: 'Ettivr',
      rating: 4.6,
      students: 1800,
      isEnrolled: false,
      isPaid: false,
      progress: 0,
      category: 'Psychology'
    }
  ]);

  // Booking state
  const [bookings, setBookings] = useState<Booking[]>([
    {
      id: '1',
      date: '2024-01-15',
      time: '14:00',
      sessionType: 'One-on-One Coaching',
      duration: '60 minutes',
      status: 'approved',
      notes: 'Focus on risk management strategies',
      createdAt: '2024-01-10'
    },
    {
      id: '2',
      date: '2024-01-20',
      time: '10:00',
      sessionType: 'Strategy Review',
      duration: '30 minutes',
      status: 'pending',
      notes: 'Review current trading performance',
      createdAt: '2024-01-12'
    },
    {
      id: '3',
      date: '2024-01-08',
      time: '16:00',
      sessionType: 'Technical Analysis Session',
      duration: '45 minutes',
      status: 'rejected',
      notes: 'Requested time slot not available',
      createdAt: '2024-01-05'
    }
  ]);

  const [newBooking, setNewBooking] = useState({
    date: '',
    time: '',
    sessionType: '',
    duration: '',
    notes: ''
  });

  // Course management functions
  const handleEnrollCourse = (courseId: string) => {
    const course = courses.find(c => c.id === courseId);
    if (course) {
      openPaymentModal(course);
    }
  };

  const handlePayForCourse = (courseId: string) => {
    const course = courses.find(c => c.id === courseId);
    if (course) {
      openPaymentModal(course);
    }
  };

  // Enquiry form state
  const [enquiryForm, setEnquiryForm] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    subject: '',
    priority: 'low',
    message: '',
    newsletter: false,
    terms: false
  });

  const [enquirySubmitted, setEnquirySubmitted] = useState(false);

  // Course tabs and video state
  const [activeTab, setActiveTab] = useState<'active' | 'suggested'>('active');
  const [selectedCourse, setSelectedCourse] = useState<Course | null>(null);
  const [currentLesson, setCurrentLesson] = useState<Lesson | null>(null);
  const [showVideoPlayer, setShowVideoPlayer] = useState(false);

  // Payment modal state
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [selectedCourseForPayment, setSelectedCourseForPayment] = useState<Course | null>(null);
  const [paymentForm, setPaymentForm] = useState({
    name: '',
    email: '',
    phone: '',
    agreeToTerms: false
  });
  const [paymentLoading, setPaymentLoading] = useState(false);
  const [paymentSuccess, setPaymentSuccess] = useState(false);
  const [paymentError, setPaymentError] = useState('');

  // Coaching state
  const [coaches] = useState<Coach[]>([
    {
      id: 'c1',
      name: 'Ettivr',
      title: 'Senior Trading Strategist',
      experience: '8+ years',
      rating: 4.9,
      totalSessions: 1250,
      specialties: ['GOAT Strategy', 'Risk Management', 'Technical Analysis', 'Market Psychology'],
      avatar: '/api/placeholder/150/150',
      hourlyRate: 150,
      availability: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
      bio: 'Expert trader with over 8 years of experience in forex markets. Creator of the GOAT trading strategy with a proven track record of consistent profits.'
    },
    {
      id: 'c2',
      name: 'Sarah Johnson',
      title: 'Risk Management Specialist',
      experience: '6+ years',
      rating: 4.8,
      totalSessions: 890,
      specialties: ['Risk Management', 'Portfolio Optimization', 'Trading Psychology'],
      avatar: '/api/placeholder/150/150',
      hourlyRate: 120,
      availability: ['Monday', 'Wednesday', 'Friday', 'Saturday'],
      bio: 'Specialized in helping traders develop robust risk management strategies and maintain emotional discipline in trading.'
    },
    {
      id: 'c3',
      name: 'Michael Chen',
      title: 'Technical Analysis Expert',
      experience: '10+ years',
      rating: 4.9,
      totalSessions: 1500,
      specialties: ['Technical Analysis', 'Chart Patterns', 'Algorithmic Trading'],
      avatar: '/api/placeholder/150/150',
      hourlyRate: 180,
      availability: ['Tuesday', 'Thursday', 'Friday', 'Sunday'],
      bio: 'Master of technical analysis with extensive experience in developing and implementing algorithmic trading systems.'
    }
  ]);

  const [coachingSessions, setCoachingSessions] = useState<CoachingSession[]>([
    {
      id: 'cs1',
      coachId: 'c1',
      date: '2024-01-25',
      time: '14:00',
      duration: 60,
      type: 'Strategy Review',
      status: 'scheduled',
      notes: 'Review current GOAT strategy implementation',
      meetingLink: 'https://meet.google.com/abc-defg-hij',
      price: 150
    },
    {
      id: 'cs2',
      coachId: 'c2',
      date: '2024-01-20',
      time: '10:00',
      duration: 45,
      type: 'Risk Assessment',
      status: 'completed',
      notes: 'Discussed portfolio risk management',
      price: 90
    }
  ]);

  const [selectedCoach, setSelectedCoach] = useState<Coach | null>(null);
  const [showBookingModal, setShowBookingModal] = useState(false);
  const [bookingForm, setBookingForm] = useState({
    date: '',
    time: '',
    duration: 60,
    sessionType: '',
    notes: '',
    agreeToTerms: false
  });

  // Sample lessons data
  const sampleLessons: { [courseId: string]: Lesson[] } = {
    '1': [
      {
        id: 'l1',
        title: 'Introduction to GOAT Strategy',
        description: 'Learn the fundamentals of the GOAT trading strategy',
        duration: '15:30',
        isCompleted: true,
        progress: 100,
        videoUrl: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
        pdfUrl: '/pdfs/goat-intro.pdf',
        isLocked: false
      },
      {
        id: 'l2',
        title: 'Market Analysis Techniques',
        description: 'Advanced market analysis for GOAT strategy',
        duration: '22:15',
        isCompleted: true,
        progress: 100,
        videoUrl: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_2mb.mp4',
        pdfUrl: '/pdfs/market-analysis.pdf',
        isLocked: false
      },
      {
        id: 'l3',
        title: 'Risk Management in GOAT',
        description: 'Managing risk with the GOAT strategy',
        duration: '18:45',
        isCompleted: false,
        progress: 75,
        videoUrl: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
        pdfUrl: '/pdfs/risk-management.pdf',
        isLocked: false
      },
      {
        id: 'l4',
        title: 'Advanced GOAT Techniques',
        description: 'Master advanced GOAT trading techniques',
        duration: '25:30',
        isCompleted: false,
        progress: 0,
        videoUrl: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_2mb.mp4',
        pdfUrl: '/pdfs/advanced-goat.pdf',
        isLocked: true
      }
    ],
    '2': [
      {
        id: 'l5',
        title: 'What is Forex Trading?',
        description: 'Complete introduction to forex markets',
        duration: '12:00',
        isCompleted: false,
        progress: 0,
        videoUrl: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
        pdfUrl: '/pdfs/forex-intro.pdf',
        isLocked: false
      }
    ]
  };

  // Helper functions
  const handleCourseClick = (course: Course) => {
    setSelectedCourse(course);
    const lessons = sampleLessons[course.id] || [];
    if (lessons.length > 0) {
      setCurrentLesson(lessons[0]);
    }
    setShowVideoPlayer(true);
  };

  const handleLessonClick = (lesson: Lesson) => {
    if (!lesson.isLocked) {
      setCurrentLesson(lesson);
    }
  };

  const downloadPDF = (pdfUrl: string, lessonTitle: string) => {
    // In a real app, this would handle the actual PDF download
    const link = document.createElement('a');
    link.href = pdfUrl;
    link.download = `${lessonTitle}.pdf`;
    link.click();
  };

  // Payment functions
  const openPaymentModal = (course: Course) => {
    setSelectedCourseForPayment(course);
    setShowPaymentModal(true);
    setPaymentForm({
      name: user?.name || '',
      email: user?.email || '',
      phone: '',
      agreeToTerms: false
    });
    setPaymentSuccess(false);
    setPaymentError('');
  };

  const closePaymentModal = () => {
    setShowPaymentModal(false);
    setSelectedCourseForPayment(null);
    setPaymentLoading(false);
    setPaymentSuccess(false);
    setPaymentError('');
    setPaymentForm({
      name: '',
      email: '',
      phone: '',
      agreeToTerms: false
    });
  };

  // Coaching booking functions
  const openBookingModal = (coach: Coach) => {
    setSelectedCoach(coach);
    setShowBookingModal(true);
    setBookingForm({
      date: '',
      time: '',
      duration: 60,
      sessionType: '',
      notes: '',
      agreeToTerms: false
    });
  };

  const closeBookingModal = () => {
    setShowBookingModal(false);
    setSelectedCoach(null);
    setBookingForm({
      date: '',
      time: '',
      duration: 60,
      sessionType: '',
      notes: '',
      agreeToTerms: false
    });
  };

  const handleBookingSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedCoach || !bookingForm.agreeToTerms) return;

    const newSession: CoachingSession = {
      id: `cs${Date.now()}`,
      coachId: selectedCoach.id,
      date: bookingForm.date,
      time: bookingForm.time,
      duration: bookingForm.duration,
      type: bookingForm.sessionType,
      status: 'scheduled',
      notes: bookingForm.notes,
      meetingLink: `https://meet.google.com/${Math.random().toString(36).substr(2, 9)}`,
      price: selectedCoach.hourlyRate * (bookingForm.duration / 60)
    };

    setCoachingSessions([...coachingSessions, newSession]);
    closeBookingModal();
  };

  const handlePaymentSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!paymentForm.agreeToTerms) {
      setPaymentError('Please agree to the terms and conditions');
      return;
    }

    if (!selectedCourseForPayment) return;

    setPaymentLoading(true);
    setPaymentError('');

    try {
      // Simulate M-Pesa payment process
      console.log('Initiating M-Pesa payment:', {
        course: selectedCourseForPayment.title,
        amount: selectedCourseForPayment.price,
        phone: paymentForm.phone,
        name: paymentForm.name,
        email: paymentForm.email
      });

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Simulate successful payment
      if (Math.random() > 0.1) { // 90% success rate for demo
        setPaymentSuccess(true);

        // Update course status
        setCourses(courses.map(course =>
          course.id === selectedCourseForPayment.id
            ? { ...course, isEnrolled: true, isPaid: true }
            : course
        ));

        // Close modal after 2 seconds
        setTimeout(() => {
          closePaymentModal();
        }, 2000);
      } else {
        throw new Error('Payment failed. Please try again.');
      }
    } catch (error) {
      setPaymentError(error instanceof Error ? error.message : 'Payment failed. Please try again.');
    } finally {
      setPaymentLoading(false);
    }
  };

  const handleEnquirySubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Here you would typically send the form data to your backend
    console.log('Enquiry submitted:', enquiryForm);
    setEnquirySubmitted(true);

    // Reset form after 3 seconds
    setTimeout(() => {
      setEnquirySubmitted(false);
      setEnquiryForm({
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        subject: '',
        priority: 'low',
        message: '',
        newsletter: false,
        terms: false
      });
    }, 3000);
  };

  const menuItems = [
    { id: 'dashboard', name: 'Dashboard', icon: '📊' },
    { id: 'signals', name: 'Signals', icon: '📈', description: 'Premium trading signals' },
    { id: 'course', name: 'Course', icon: '📚', description: 'Full GOAT strategy' },
    { id: 'one-on-one', name: 'One on One', icon: '👥', description: 'Personal coaching' },
    { id: 'trade-with-ettivr', name: 'Trade With Ettivr', icon: '🎯', description: 'Live trading sessions' },
    { id: 'collaborations', name: 'Collaborations', icon: '🤝', description: 'Brand partnerships' },
    { id: 'academy', name: 'Academy', icon: '🎓', description: 'Beginner training' },
    { id: 'booking', name: 'Booking', icon: '📅', description: 'Schedule sessions' },
    { id: 'enquiry', name: 'Enquiry', icon: '❓', description: 'Get in touch' },
    { id: 'settings', name: 'Settings', icon: '⚙️', description: 'Account & preferences' }
  ];

  const renderContent = () => {
    switch (activeSection) {
      case 'signals':
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-dark-900 dark:text-white">Premium Trading Signals</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[1, 2, 3].map((signal) => (
                <div key={signal} className="card p-6">
                  <div className="flex items-center justify-between mb-4">
                    <span className="text-lg font-semibold text-gray-900 dark:text-white">EUR/USD</span>
                    <span className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 px-2 py-1 rounded-full text-sm">BUY</span>
                  </div>
                  <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                    <div className="flex justify-between">
                      <span>Entry:</span>
                      <span className="font-medium">1.0850</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Stop Loss:</span>
                      <span className="font-medium">1.0820</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Take Profit:</span>
                      <span className="font-medium">1.0920</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        );
      case 'course':
        const activeCourses = courses.filter(course => course.isEnrolled && course.isPaid);
        const suggestedCourses = courses.filter(course => !course.isEnrolled || !course.isPaid);

        if (showVideoPlayer && selectedCourse && currentLesson) {
          return (
            <div className="space-y-6">
              {/* Video Player Header */}
              <div className="flex items-center justify-between">
                <button
                  onClick={() => setShowVideoPlayer(false)}
                  className="flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                  <span>Back to Courses</span>
                </button>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">{selectedCourse.title}</h2>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Video Player */}
                <div className="lg:col-span-2 space-y-4">
                  <div className="card p-6">
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">{currentLesson.title}</h3>

                    {/* Video Container */}
                    <div className="aspect-video bg-gray-900 rounded-lg mb-4 flex items-center justify-center">
                      <video
                        controls
                        className="w-full h-full rounded-lg"
                        poster="/api/placeholder/800/450"
                      >
                        <source src={currentLesson.videoUrl} type="video/mp4" />
                        Your browser does not support the video tag.
                      </video>
                    </div>

                    {/* Lesson Info */}
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-4">
                        <span className="text-sm text-gray-600 dark:text-gray-400">Duration: {currentLesson.duration}</span>
                        <span className="text-sm text-gray-600 dark:text-gray-400">Progress: {currentLesson.progress}%</span>
                      </div>
                      <button
                        onClick={() => downloadPDF(currentLesson.pdfUrl!, currentLesson.title)}
                        className="flex items-center space-x-2 bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors"
                      >
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                        </svg>
                        <span>Download PDF</span>
                      </button>
                    </div>

                    {/* Progress Bar */}
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-4">
                      <div
                        className="bg-primary-500 h-2 rounded-full transition-all duration-300"
                        style={{width: `${currentLesson.progress}%`}}
                      ></div>
                    </div>

                    <p className="text-gray-600 dark:text-gray-400">{currentLesson.description}</p>
                  </div>
                </div>

                {/* Course Lessons Sidebar */}
                <div className="space-y-4">
                  <div className="card p-6">
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Course Lessons</h4>
                    <div className="space-y-3">
                      {(sampleLessons[selectedCourse.id] || []).map((lesson, index) => (
                        <div
                          key={lesson.id}
                          onClick={() => handleLessonClick(lesson)}
                          className={`p-3 rounded-lg cursor-pointer transition-colors ${
                            currentLesson.id === lesson.id
                              ? 'bg-primary-100 dark:bg-primary-900 border-2 border-primary-500'
                              : lesson.isLocked
                                ? 'bg-gray-100 dark:bg-gray-700 opacity-60 cursor-not-allowed'
                                : 'bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600'
                          }`}
                        >
                          <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0">
                              {lesson.isLocked ? (
                                <svg className="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                                  <path d="M12,17A2,2 0 0,0 14,15C14,13.89 13.1,13 12,13A2,2 0 0,0 10,15A2,2 0 0,0 12,17M18,8A2,2 0 0,1 20,10V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V10C4,8.89 4.9,8 6,8H7V6A5,5 0 0,1 12,1A5,5 0 0,1 17,6V8H18M12,3A3,3 0 0,0 9,6V8H15V6A3,3 0 0,0 12,3Z"/>
                                </svg>
                              ) : lesson.isCompleted ? (
                                <svg className="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 24 24">
                                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                </svg>
                              ) : (
                                <svg className="w-5 h-5 text-primary-500" fill="currentColor" viewBox="0 0 24 24">
                                  <path d="M8 5v14l11-7z"/>
                                </svg>
                              )}
                            </div>
                            <div className="flex-1 min-w-0">
                              <h5 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                                {index + 1}. {lesson.title}
                              </h5>
                              <p className="text-xs text-gray-600 dark:text-gray-400">{lesson.duration}</p>
                              {lesson.progress > 0 && lesson.progress < 100 && (
                                <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-1 mt-1">
                                  <div
                                    className="bg-primary-500 h-1 rounded-full"
                                    style={{width: `${lesson.progress}%`}}
                                  ></div>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          );
        }

        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">My Courses</h2>
            </div>

            {/* Tabs */}
            <div className="border-b border-gray-200 dark:border-gray-700">
              <nav className="-mb-px flex space-x-8">
                <button
                  onClick={() => setActiveTab('active')}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === 'active'
                      ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                      : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300'
                  }`}
                >
                  Active Courses ({activeCourses.length})
                </button>
                <button
                  onClick={() => setActiveTab('suggested')}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === 'suggested'
                      ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                      : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300'
                  }`}
                >
                  Suggested Courses ({suggestedCourses.length})
                </button>
              </nav>
            </div>

            {/* Tab Content */}
            {activeTab === 'active' ? (
              activeCourses.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {activeCourses.map((course) => (
                    <div key={course.id} className="card p-6 hover:shadow-lg transition-shadow">
                      <div className="flex items-center justify-between mb-4">
                        <h4 className="text-lg font-semibold text-gray-900 dark:text-white">{course.title}</h4>
                        <span className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 px-3 py-1 rounded-full text-sm font-medium">
                          Active
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">{course.description}</p>
                      <div className="space-y-3">
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600 dark:text-gray-400">Progress</span>
                          <span className="text-primary-600 font-semibold">{course.progress}%</span>
                        </div>
                        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                          <div
                            className="bg-gradient-to-r from-primary-500 to-primary-600 h-2 rounded-full"
                            style={{width: `${course.progress}%`}}
                          ></div>
                        </div>
                        <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
                          <span>{course.modules} modules • {course.lessons} lessons</span>
                          <span>{course.duration}</span>
                        </div>
                        <button
                          onClick={() => handleCourseClick(course)}
                          className="w-full bg-primary-600 text-white py-2 px-4 rounded-lg hover:bg-primary-700 transition-colors"
                        >
                          Continue Learning
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <div className="text-6xl mb-4">📚</div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">No Active Courses</h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-6">
                    You don't have any active courses yet. Check out our suggested courses below!
                  </p>
                  <button
                    onClick={() => setActiveTab('suggested')}
                    className="bg-primary-600 text-white py-2 px-6 rounded-lg hover:bg-primary-700 transition-colors"
                  >
                    View Suggested Courses
                  </button>
                </div>
              )
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {suggestedCourses.map((course) => (
                  <div key={course.id} className="card p-6 hover:shadow-lg transition-shadow">
                    <div className="aspect-video bg-gray-200 dark:bg-gray-700 rounded-lg mb-4 flex items-center justify-center">
                      <svg className="w-12 h-12 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M8 5v14l11-7z"/>
                      </svg>
                    </div>

                    <div className="space-y-3">
                      <div className="flex items-start justify-between">
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{course.title}</h3>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          course.level === 'Beginner' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                          course.level === 'Intermediate' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                          'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                        }`}>
                          {course.level}
                        </span>
                      </div>

                      <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">{course.description}</p>

                      <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
                        <div className="flex items-center space-x-1">
                          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                          </svg>
                          <span>{course.rating}</span>
                        </div>
                        <span>•</span>
                        <span>{course.students} students</span>
                      </div>

                      <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
                        <span>{course.modules} modules • {course.lessons} lessons</span>
                        <span>{course.duration}</span>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-2xl font-bold text-gray-900 dark:text-white">${course.price}</span>
                        <span className="text-sm text-gray-600 dark:text-gray-400">by {course.instructor}</span>
                      </div>

                      {course.isEnrolled ? (
                        course.isPaid ? (
                          <button
                            onClick={() => handleCourseClick(course)}
                            className="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors"
                          >
                            Continue Learning
                          </button>
                        ) : (
                          <button
                            onClick={() => handlePayForCourse(course.id)}
                            className="w-full bg-yellow-600 text-white py-2 px-4 rounded-lg hover:bg-yellow-700 transition-colors"
                          >
                            Complete Payment
                          </button>
                        )
                      ) : (
                        <button
                          onClick={() => handleEnrollCourse(course.id)}
                          className="w-full bg-primary-600 text-white py-2 px-4 rounded-lg hover:bg-primary-700 transition-colors"
                        >
                          Enroll Now
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        );
      case 'academy':
        const availableCourses = courses.filter(course => !course.isEnrolled);
        const categories = [...new Set(courses.map(course => course.category))];

        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Academy</h2>
              <p className="text-gray-600 dark:text-gray-400">Discover and enroll in our comprehensive trading courses</p>
            </div>

            {/* Course Categories Filter */}
            <div className="flex flex-wrap gap-2">
              <button className="px-4 py-2 bg-primary-600 text-white rounded-lg text-sm font-medium">
                All Courses
              </button>
              {categories.map((category) => (
                <button
                  key={category}
                  className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg text-sm font-medium hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
                >
                  {category}
                </button>
              ))}
            </div>

            {/* Available Courses Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {courses.map((course) => (
                <div key={course.id} className="card p-6 hover:shadow-lg transition-shadow">
                  <div className="aspect-video bg-gray-200 dark:bg-gray-700 rounded-lg mb-4 flex items-center justify-center">
                    <svg className="w-12 h-12 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M8 5v14l11-7z"/>
                    </svg>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-start justify-between">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{course.title}</h3>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        course.level === 'Beginner' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                        course.level === 'Intermediate' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                        'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                      }`}>
                        {course.level}
                      </span>
                    </div>

                    <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">{course.description}</p>

                    <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
                      <div className="flex items-center space-x-1">
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                        <span>{course.rating}</span>
                      </div>
                      <span>•</span>
                      <span>{course.students} students</span>
                    </div>

                    <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
                      <span>{course.modules} modules • {course.lessons} lessons</span>
                      <span>{course.duration}</span>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-2xl font-bold text-gray-900 dark:text-white">${course.price}</span>
                      <span className="text-sm text-gray-600 dark:text-gray-400">by {course.instructor}</span>
                    </div>

                    {course.isEnrolled ? (
                      course.isPaid ? (
                        <button
                          onClick={() => setActiveSection('course')}
                          className="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors"
                        >
                          Continue Learning
                        </button>
                      ) : (
                        <button
                          onClick={() => handlePayForCourse(course.id)}
                          className="w-full bg-yellow-600 text-white py-2 px-4 rounded-lg hover:bg-yellow-700 transition-colors"
                        >
                          Complete Payment
                        </button>
                      )
                    ) : (
                      <button
                        onClick={() => handleEnrollCourse(course.id)}
                        className="w-full bg-primary-600 text-white py-2 px-4 rounded-lg hover:bg-primary-700 transition-colors"
                      >
                        Enroll Now
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {/* No courses message */}
            {availableCourses.length === 0 && (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">🎓</div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">All Courses Enrolled</h3>
                <p className="text-gray-600 dark:text-gray-400">
                  You're enrolled in all available courses. Check back later for new content!
                </p>
              </div>
            )}
          </div>
        );
      case 'one-on-one':
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">One-on-One Coaching</h2>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Get personalized trading guidance from expert coaches
              </div>
            </div>

            {/* Upcoming Sessions */}
            {coachingSessions.filter(session => session.status === 'scheduled').length > 0 && (
              <div className="space-y-4">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Upcoming Sessions</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {coachingSessions
                    .filter(session => session.status === 'scheduled')
                    .map((session) => {
                      const coach = coaches.find(c => c.id === session.coachId);
                      return (
                        <div key={session.id} className="card p-6 border-l-4 border-primary-500">
                          <div className="flex items-start justify-between mb-4">
                            <div>
                              <h4 className="font-semibold text-gray-900 dark:text-white">{session.type}</h4>
                              <p className="text-sm text-gray-600 dark:text-gray-400">with {coach?.name}</p>
                            </div>
                            <span className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 px-2 py-1 rounded-full text-xs font-medium">
                              Scheduled
                            </span>
                          </div>
                          <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                            <div className="flex items-center space-x-2">
                              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                              </svg>
                              <span>{new Date(session.date).toLocaleDateString()}</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                              </svg>
                              <span>{session.time} ({session.duration} minutes)</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"/>
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm-7-8a7 7 0 1114 0 7 7 0 01-14 0z" clipRule="evenodd" />
                              </svg>
                              <span>${session.price}</span>
                            </div>
                          </div>
                          {session.notes && (
                            <div className="mt-3 text-sm text-gray-600 dark:text-gray-400">
                              <span className="font-medium">Notes:</span> {session.notes}
                            </div>
                          )}
                          <div className="mt-4 flex space-x-2">
                            <button className="flex-1 bg-primary-600 text-white py-2 px-4 rounded-lg hover:bg-primary-700 transition-colors text-sm">
                              Join Session
                            </button>
                            <button className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors text-sm">
                              Reschedule
                            </button>
                          </div>
                        </div>
                      );
                    })}
                </div>
              </div>
            )}

            {/* Available Coaches */}
            <div className="space-y-4">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Available Coaches</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {coaches.map((coach) => (
                  <div key={coach.id} className="card p-6 hover:shadow-lg transition-shadow">
                    <div className="flex items-center space-x-4 mb-4">
                      <div className="w-16 h-16 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                        <span className="text-xl font-bold text-gray-600 dark:text-gray-300">
                          {coach.name.charAt(0)}
                        </span>
                      </div>
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900 dark:text-white">{coach.name}</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400">{coach.title}</p>
                        <div className="flex items-center space-x-2 mt-1">
                          <div className="flex items-center space-x-1">
                            <svg className="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                            </svg>
                            <span className="text-sm text-gray-600 dark:text-gray-400">{coach.rating}</span>
                          </div>
                          <span className="text-sm text-gray-600 dark:text-gray-400">•</span>
                          <span className="text-sm text-gray-600 dark:text-gray-400">{coach.totalSessions} sessions</span>
                        </div>
                      </div>
                    </div>

                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">{coach.bio}</p>

                    <div className="space-y-3">
                      <div>
                        <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Specialties</h5>
                        <div className="flex flex-wrap gap-1">
                          {coach.specialties.map((specialty, index) => (
                            <span
                              key={index}
                              className="px-2 py-1 bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200 rounded-full text-xs"
                            >
                              {specialty}
                            </span>
                          ))}
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <span className="text-sm text-gray-600 dark:text-gray-400">Experience:</span>
                          <span className="ml-1 font-medium text-gray-900 dark:text-white">{coach.experience}</span>
                        </div>
                        <div className="text-right">
                          <div className="text-lg font-bold text-gray-900 dark:text-white">${coach.hourlyRate}/hr</div>
                        </div>
                      </div>

                      <div>
                        <span className="text-sm text-gray-600 dark:text-gray-400">Available:</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {coach.availability.map((day, index) => (
                            <span
                              key={index}
                              className="px-2 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded text-xs"
                            >
                              {day.slice(0, 3)}
                            </span>
                          ))}
                        </div>
                      </div>

                      <button
                        onClick={() => openBookingModal(coach)}
                        className="w-full bg-primary-600 text-white py-2 px-4 rounded-lg hover:bg-primary-700 transition-colors"
                      >
                        Book Session
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Session History */}
            {coachingSessions.filter(session => session.status === 'completed').length > 0 && (
              <div className="space-y-4">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Session History</h3>
                <div className="card p-6">
                  <div className="space-y-4">
                    {coachingSessions
                      .filter(session => session.status === 'completed')
                      .map((session) => {
                        const coach = coaches.find(c => c.id === session.coachId);
                        return (
                          <div key={session.id} className="flex items-center justify-between py-3 border-b border-gray-200 dark:border-gray-700 last:border-b-0">
                            <div>
                              <h4 className="font-medium text-gray-900 dark:text-white">{session.type}</h4>
                              <p className="text-sm text-gray-600 dark:text-gray-400">
                                with {coach?.name} • {new Date(session.date).toLocaleDateString()} • {session.duration} min
                              </p>
                            </div>
                            <div className="text-right">
                              <span className="text-sm font-medium text-gray-900 dark:text-white">${session.price}</span>
                              <div className="text-xs text-green-600 dark:text-green-400">Completed</div>
                            </div>
                          </div>
                        );
                      })}
                  </div>
                </div>
              </div>
            )}
          </div>
        );
      case 'settings':
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Settings</h2>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Account Settings */}
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Account Settings</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Full Name
                    </label>
                    <input
                      type="text"
                      defaultValue={user?.name || 'Chris Mburu'}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Email Address
                    </label>
                    <input
                      type="email"
                      defaultValue={user?.email || '<EMAIL>'}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Phone Number
                    </label>
                    <input
                      type="tel"
                      placeholder="+****************"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>
                  <button className="btn-primary w-full">Update Account</button>
                </div>
              </div>

              {/* Preferences */}
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Preferences</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Dark Mode
                      </label>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Toggle between light and dark theme
                      </p>
                    </div>
                    <button
                      onClick={toggleTheme}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                        isDark ? 'bg-primary-600' : 'bg-gray-200'
                      }`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                          isDark ? 'translate-x-6' : 'translate-x-1'
                        }`}
                      />
                    </button>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Email Notifications
                      </label>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Receive trading signals via email
                      </p>
                    </div>
                    <button className="relative inline-flex h-6 w-11 items-center rounded-full bg-primary-600">
                      <span className="inline-block h-4 w-4 transform rounded-full bg-white translate-x-6" />
                    </button>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        SMS Alerts
                      </label>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Get urgent signals via SMS
                      </p>
                    </div>
                    <button className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200">
                      <span className="inline-block h-4 w-4 transform rounded-full bg-white translate-x-1" />
                    </button>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Trading Experience Level
                    </label>
                    <select className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                      <option>Beginner</option>
                      <option>Intermediate</option>
                      <option>Advanced</option>
                      <option>Professional</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>

            {/* Security Settings */}
            <div className="card p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Security</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Current Password
                    </label>
                    <input
                      type="password"
                      placeholder="Enter current password"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      New Password
                    </label>
                    <input
                      type="password"
                      placeholder="Enter new password"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Confirm New Password
                    </label>
                    <input
                      type="password"
                      placeholder="Confirm new password"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>
                  <button className="btn-primary w-full">Change Password</button>
                </div>

                <div className="space-y-4">
                  <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                    <h4 className="text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-2">
                      Two-Factor Authentication
                    </h4>
                    <p className="text-xs text-yellow-700 dark:text-yellow-300 mb-3">
                      Add an extra layer of security to your account
                    </p>
                    <button className="btn-secondary text-sm">Enable 2FA</button>
                  </div>

                  <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                    <h4 className="text-sm font-medium text-red-800 dark:text-red-200 mb-2">
                      Danger Zone
                    </h4>
                    <p className="text-xs text-red-700 dark:text-red-300 mb-3">
                      Permanently delete your account and all data
                    </p>
                    <button className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm transition-colors">
                      Delete Account
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      case 'booking':
        const handleBookingSubmit = (e: React.FormEvent) => {
          e.preventDefault();
          const booking: Booking = {
            id: Date.now().toString(),
            ...newBooking,
            status: 'pending',
            createdAt: new Date().toISOString().split('T')[0]
          };
          setBookings([booking, ...bookings]);
          setNewBooking({
            date: '',
            time: '',
            sessionType: '',
            duration: '',
            notes: ''
          });
        };

        const getStatusColor = (status: string) => {
          switch (status) {
            case 'approved': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
            case 'rejected': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
            default: return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
          }
        };

        const getStatusIcon = (status: string) => {
          switch (status) {
            case 'approved':
              return (
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              );
            case 'rejected':
              return (
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              );
            default:
              return (
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                </svg>
              );
          }
        };

        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Book a Session</h2>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Schedule your personal coaching session
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Booking Form */}
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Schedule New Session
                </h3>
                <form onSubmit={handleBookingSubmit} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Date *
                      </label>
                      <input
                        type="date"
                        value={newBooking.date}
                        onChange={(e) => setNewBooking({...newBooking, date: e.target.value})}
                        min={new Date().toISOString().split('T')[0]}
                        required
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Time *
                      </label>
                      <select
                        value={newBooking.time}
                        onChange={(e) => setNewBooking({...newBooking, time: e.target.value})}
                        required
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      >
                        <option value="">Select time</option>
                        <option value="09:00">9:00 AM</option>
                        <option value="10:00">10:00 AM</option>
                        <option value="11:00">11:00 AM</option>
                        <option value="14:00">2:00 PM</option>
                        <option value="15:00">3:00 PM</option>
                        <option value="16:00">4:00 PM</option>
                        <option value="17:00">5:00 PM</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Session Type *
                    </label>
                    <select
                      value={newBooking.sessionType}
                      onChange={(e) => setNewBooking({...newBooking, sessionType: e.target.value})}
                      required
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="">Select session type</option>
                      <option value="One-on-One Coaching">One-on-One Coaching</option>
                      <option value="Strategy Review">Strategy Review</option>
                      <option value="Technical Analysis Session">Technical Analysis Session</option>
                      <option value="Risk Management Consultation">Risk Management Consultation</option>
                      <option value="Portfolio Review">Portfolio Review</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Duration *
                    </label>
                    <select
                      value={newBooking.duration}
                      onChange={(e) => setNewBooking({...newBooking, duration: e.target.value})}
                      required
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="">Select duration</option>
                      <option value="30 minutes">30 minutes</option>
                      <option value="45 minutes">45 minutes</option>
                      <option value="60 minutes">60 minutes</option>
                      <option value="90 minutes">90 minutes</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Notes (Optional)
                    </label>
                    <textarea
                      value={newBooking.notes}
                      onChange={(e) => setNewBooking({...newBooking, notes: e.target.value})}
                      rows={3}
                      placeholder="Any specific topics you'd like to discuss..."
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>

                  <button
                    type="submit"
                    className="w-full btn-primary"
                  >
                    Submit Booking Request
                  </button>
                </form>
              </div>

              {/* Available Time Slots */}
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Available Time Slots
                </h3>
                <div className="space-y-3">
                  <div className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                    All times are in your local timezone
                  </div>

                  {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'].map((day) => (
                    <div key={day} className="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-700">
                      <span className="font-medium text-gray-900 dark:text-white">{day}</span>
                      <span className="text-sm text-gray-600 dark:text-gray-400">9:00 AM - 5:00 PM</span>
                    </div>
                  ))}

                  <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-1">
                      Booking Guidelines
                    </h4>
                    <ul className="text-xs text-blue-700 dark:text-blue-300 space-y-1">
                      <li>• Book at least 24 hours in advance</li>
                      <li>• Sessions can be rescheduled up to 2 hours before</li>
                      <li>• You'll receive confirmation via email</li>
                      <li>• Meeting link will be provided upon approval</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            {/* Booking History */}
            <div className="card p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Your Booking History
              </h3>
              <div className="space-y-4">
                {bookings.map((booking) => (
                  <div key={booking.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h4 className="font-semibold text-gray-900 dark:text-white">
                            {booking.sessionType}
                          </h4>
                          <span className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(booking.status)}`}>
                            {getStatusIcon(booking.status)}
                            <span className="capitalize">{booking.status}</span>
                          </span>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 dark:text-gray-400">
                          <div className="flex items-center space-x-2">
                            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                            </svg>
                            <span>{new Date(booking.date).toLocaleDateString()}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                            </svg>
                            <span>{booking.time} ({booking.duration})</span>
                          </div>
                          <div className="text-xs text-gray-500">
                            Requested: {new Date(booking.createdAt).toLocaleDateString()}
                          </div>
                        </div>
                        {booking.notes && (
                          <div className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                            <span className="font-medium">Notes:</span> {booking.notes}
                          </div>
                        )}
                      </div>
                      {booking.status === 'approved' && (
                        <button className="ml-4 bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm transition-colors">
                          Join Session
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );
      case 'enquiry':
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Contact & Enquiry</h2>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Get in touch with our team
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Contact Information */}
              <div className="space-y-6">
                <div className="card p-6">
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">Get In Touch</h3>

                  <div className="space-y-4">
                    {/* Email */}
                    <div className="flex items-start space-x-4">
                      <div className="w-10 h-10 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center flex-shrink-0">
                        <svg className="w-5 h-5 text-primary-600 dark:text-primary-400" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                        </svg>
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900 dark:text-white">Email Support</h4>
                        <p className="text-gray-600 dark:text-gray-400"><EMAIL></p>
                        <p className="text-sm text-gray-500 dark:text-gray-500">We'll respond within 24 hours</p>
                      </div>
                    </div>

                    {/* Phone */}
                    <div className="flex items-start space-x-4">
                      <div className="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center flex-shrink-0">
                        <svg className="w-5 h-5 text-green-600 dark:text-green-400" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z"/>
                        </svg>
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900 dark:text-white">Phone Support</h4>
                        <p className="text-gray-600 dark:text-gray-400">+****************</p>
                        <p className="text-sm text-gray-500 dark:text-gray-500">Mon-Fri, 9AM-6PM EST</p>
                      </div>
                    </div>

                    {/* Live Chat */}
                    <div className="flex items-start space-x-4">
                      <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center flex-shrink-0">
                        <svg className="w-5 h-5 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z"/>
                        </svg>
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900 dark:text-white">Live Chat</h4>
                        <p className="text-gray-600 dark:text-gray-400">Chat with our AI assistant</p>
                        <p className="text-sm text-gray-500 dark:text-gray-500">Available 24/7</p>
                      </div>
                    </div>

                    {/* Office Hours */}
                    <div className="flex items-start space-x-4">
                      <div className="w-10 h-10 bg-yellow-100 dark:bg-yellow-900 rounded-lg flex items-center justify-center flex-shrink-0">
                        <svg className="w-5 h-5 text-yellow-600 dark:text-yellow-400" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"/>
                          <path d="M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67z"/>
                        </svg>
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900 dark:text-white">Business Hours</h4>
                        <p className="text-gray-600 dark:text-gray-400">Monday - Friday: 9:00 AM - 6:00 PM</p>
                        <p className="text-gray-600 dark:text-gray-400">Saturday: 10:00 AM - 4:00 PM</p>
                        <p className="text-sm text-gray-500 dark:text-gray-500">Eastern Standard Time</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* FAQ Quick Links */}
                <div className="card p-6">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Quick Help</h3>
                  <div className="space-y-3">
                    <button className="w-full text-left p-3 rounded-lg bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                      <div className="font-medium text-gray-900 dark:text-white">How do I access my courses?</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">Learn about course access and navigation</div>
                    </button>
                    <button className="w-full text-left p-3 rounded-lg bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                      <div className="font-medium text-gray-900 dark:text-white">Payment and billing questions</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">Information about payments and refunds</div>
                    </button>
                    <button className="w-full text-left p-3 rounded-lg bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                      <div className="font-medium text-gray-900 dark:text-white">Technical support</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">Help with platform issues</div>
                    </button>
                  </div>
                </div>
              </div>

              {/* Contact Form */}
              <div className="card p-6">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">Send us a Message</h3>

                {enquirySubmitted ? (
                  <div className="text-center py-8">
                    <div className="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
                      <svg className="w-8 h-8 text-green-600 dark:text-green-400" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                      </svg>
                    </div>
                    <h4 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">Message Sent Successfully!</h4>
                    <p className="text-gray-600 dark:text-gray-400">
                      Thank you for contacting us. We'll get back to you within 24 hours.
                    </p>
                  </div>
                ) : (
                  <form onSubmit={handleEnquirySubmit} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          First Name *
                        </label>
                        <input
                          type="text"
                          required
                          value={enquiryForm.firstName}
                          onChange={(e) => setEnquiryForm({...enquiryForm, firstName: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          placeholder="Enter your first name"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Last Name *
                        </label>
                        <input
                          type="text"
                          required
                          value={enquiryForm.lastName}
                          onChange={(e) => setEnquiryForm({...enquiryForm, lastName: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          placeholder="Enter your last name"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Email Address *
                      </label>
                      <input
                        type="email"
                        required
                        value={enquiryForm.email}
                        onChange={(e) => setEnquiryForm({...enquiryForm, email: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        placeholder="Enter your email address"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Phone Number
                      </label>
                      <input
                        type="tel"
                        value={enquiryForm.phone}
                        onChange={(e) => setEnquiryForm({...enquiryForm, phone: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        placeholder="Enter your phone number"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Subject *
                      </label>
                      <select
                        required
                        value={enquiryForm.subject}
                        onChange={(e) => setEnquiryForm({...enquiryForm, subject: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      >
                        <option value="">Select a subject</option>
                        <option value="general">General Inquiry</option>
                        <option value="course">Course Information</option>
                        <option value="technical">Technical Support</option>
                        <option value="billing">Billing & Payments</option>
                        <option value="coaching">Personal Coaching</option>
                        <option value="partnership">Partnership Opportunities</option>
                        <option value="feedback">Feedback & Suggestions</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Priority Level
                      </label>
                      <div className="flex space-x-4">
                        <label className="flex items-center">
                          <input
                            type="radio"
                            name="priority"
                            value="low"
                            checked={enquiryForm.priority === 'low'}
                            onChange={(e) => setEnquiryForm({...enquiryForm, priority: e.target.value})}
                            className="mr-2 text-primary-600"
                          />
                          <span className="text-sm text-gray-700 dark:text-gray-300">Low</span>
                        </label>
                        <label className="flex items-center">
                          <input
                            type="radio"
                            name="priority"
                            value="medium"
                            checked={enquiryForm.priority === 'medium'}
                            onChange={(e) => setEnquiryForm({...enquiryForm, priority: e.target.value})}
                            className="mr-2 text-primary-600"
                          />
                          <span className="text-sm text-gray-700 dark:text-gray-300">Medium</span>
                        </label>
                        <label className="flex items-center">
                          <input
                            type="radio"
                            name="priority"
                            value="high"
                            checked={enquiryForm.priority === 'high'}
                            onChange={(e) => setEnquiryForm({...enquiryForm, priority: e.target.value})}
                            className="mr-2 text-primary-600"
                          />
                          <span className="text-sm text-gray-700 dark:text-gray-300">High</span>
                        </label>
                        <label className="flex items-center">
                          <input
                            type="radio"
                            name="priority"
                            value="urgent"
                            checked={enquiryForm.priority === 'urgent'}
                            onChange={(e) => setEnquiryForm({...enquiryForm, priority: e.target.value})}
                            className="mr-2 text-primary-600"
                          />
                          <span className="text-sm text-gray-700 dark:text-gray-300">Urgent</span>
                        </label>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Message *
                      </label>
                      <textarea
                        required
                        rows={6}
                        value={enquiryForm.message}
                        onChange={(e) => setEnquiryForm({...enquiryForm, message: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        placeholder="Please describe your inquiry in detail..."
                      />
                    </div>

                    <div className="flex items-start space-x-2">
                      <input
                        type="checkbox"
                        id="newsletter"
                        checked={enquiryForm.newsletter}
                        onChange={(e) => setEnquiryForm({...enquiryForm, newsletter: e.target.checked})}
                        className="mt-1 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
                      />
                      <label htmlFor="newsletter" className="text-sm text-gray-700 dark:text-gray-300">
                        I would like to receive updates about new courses, trading tips, and special offers via email.
                      </label>
                    </div>

                    <div className="flex items-start space-x-2">
                      <input
                        type="checkbox"
                        id="terms"
                        required
                        checked={enquiryForm.terms}
                        onChange={(e) => setEnquiryForm({...enquiryForm, terms: e.target.checked})}
                        className="mt-1 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
                      />
                      <label htmlFor="terms" className="text-sm text-gray-700 dark:text-gray-300">
                        I agree to the <a href="#" className="text-primary-600 hover:text-primary-700">Terms of Service</a> and <a href="#" className="text-primary-600 hover:text-primary-700">Privacy Policy</a> *
                      </label>
                    </div>

                    <button
                      type="submit"
                      className="w-full bg-primary-600 text-white py-3 px-6 rounded-lg hover:bg-primary-700 transition-colors font-medium"
                    >
                      Send Message
                    </button>
                  </form>
                )}
              </div>
            </div>

            {/* Response Time Information */}
            <div className="card p-6 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800">
              <div className="flex items-start space-x-4">
                <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center flex-shrink-0">
                  <svg className="w-5 h-5 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  </svg>
                </div>
                <div>
                  <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">Response Time Commitment</h4>
                  <div className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                    <p>• <strong>General Inquiries:</strong> Within 24 hours</p>
                    <p>• <strong>Technical Support:</strong> Within 12 hours</p>
                    <p>• <strong>Urgent Issues:</strong> Within 4 hours</p>
                    <p>• <strong>Billing Questions:</strong> Within 8 hours</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      default:
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                Welcome back, {user?.name}! 👋
              </h2>
            </div>
            <p className="text-gray-600 dark:text-gray-400">
              Here's your complete navigation hub. Click any card to access that section.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {menuItems.slice(1).map((item) => (
                <div
                  key={item.id}
                  onClick={() => setActiveSection(item.id)}
                  className="card p-6 cursor-pointer hover:shadow-lg transition-shadow duration-200"
                >
                  <div className="text-3xl mb-4">{item.icon}</div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    {item.name}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {item.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex">
      {/* Sidebar */}
      <div className="w-64 bg-white dark:bg-gray-800 shadow-lg">
        <div className="p-6">
          <div className="flex items-center space-x-3 mb-8">
            <div className="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
              </svg>
            </div>
            <div>
              <h1 className="text-lg font-bold text-gray-900 dark:text-white">E TRIBE CONCEPTS</h1>
            </div>
          </div>

          <nav className="space-y-2">
            {menuItems.map((item) => (
              <button
                key={item.id}
                onClick={() => setActiveSection(item.id)}
                className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors ${
                  activeSection === item.id
                    ? 'bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
              >
                <span className="text-xl">{item.icon}</span>
                <span className="font-medium">{item.name}</span>
              </button>
            ))}
          </nav>
        </div>

        <div className="absolute bottom-0 left-0 right-0 w-64 p-6 border-t border-gray-200 dark:border-gray-700">
          <div className="bg-primary-50 dark:bg-primary-900 rounded-lg p-4">
            <h4 className="font-semibold text-primary-900 dark:text-primary-100 mb-2">Need Help?</h4>
            <p className="text-sm text-primary-700 dark:text-primary-300 mb-3">
              Contact our support team for assistance.
            </p>
            <button className="text-sm bg-primary-600 text-white px-3 py-1 rounded">
              Contact Support
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Top Bar */}
        <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 px-6 py-3">
          <div className="flex items-center justify-between">
            {/* Left side - Empty or can add other elements */}
            <div className="flex items-center space-x-4">
              <h1 className="text-xl font-semibold text-gray-900 dark:text-white capitalize">
                {activeSection.replace('-', ' ')}
              </h1>
            </div>

            {/* Right side - Forecast, Settings, Notifications, User Profile */}
            <div className="flex items-center space-x-3">
              {/* Forecast Button */}
              <button className="flex items-center space-x-2 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M3 13h8V3H9v6H3v4zm0 8h6v-2H3v2zm10 0h8v-6h-8v6zm5-12V3h-5v6h5z"/>
                </svg>
                <span className="font-medium">Forecast</span>
                <span className="bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full">NEW</span>
              </button>
              {/* Settings */}
              <button
                onClick={toggleTheme}
                className="p-2 rounded-lg text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                title="Settings"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </button>

              {/* Notifications */}
              <button className="p-2 rounded-lg text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors relative" title="Notifications">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                </svg>
                {/* Notification dot */}
                <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
              </button>

              {/* User Profile Section */}
              <div className="flex items-center space-x-3 pl-3 border-l border-gray-200 dark:border-gray-600">
                <div className="text-right">
                  <div className="text-sm font-medium text-gray-900 dark:text-white">
                    {user?.name || 'Chris Mburu'}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {user?.email || '<EMAIL>'}
                  </div>
                </div>
                <div className="relative">
                  <button className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center text-white font-medium hover:bg-red-600 transition-colors">
                    <span className="text-sm">
                      {user?.name?.charAt(0).toUpperCase() || 'C'}
                    </span>
                  </button>
                </div>
                <button
                  onClick={logout}
                  className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                  title="Logout"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </header>

        {/* Content */}
        <main className="flex-1 p-6">
          {renderContent()}
        </main>
      </div>

      {/* E-TRIB Chatbot */}
      <ETRIBChatbot />

      {/* Booking Modal */}
      {showBookingModal && selectedCoach && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              {/* Modal Header */}
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                  Book Session with {selectedCoach.name}
                </h3>
                <button
                  onClick={closeBookingModal}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {/* Coach Info */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                    <span className="text-lg font-bold text-gray-600 dark:text-gray-300">
                      {selectedCoach.name.charAt(0)}
                    </span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 dark:text-white">{selectedCoach.name}</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{selectedCoach.title}</p>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-lg font-bold text-primary-600 dark:text-primary-400">
                    ${selectedCoach.hourlyRate}/hour
                  </span>
                  <div className="flex items-center space-x-1">
                    <svg className="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                    </svg>
                    <span className="text-sm text-gray-600 dark:text-gray-400">{selectedCoach.rating}</span>
                  </div>
                </div>
              </div>

              {/* Booking Form */}
              <form onSubmit={handleBookingSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Date *
                    </label>
                    <input
                      type="date"
                      required
                      value={bookingForm.date}
                      onChange={(e) => setBookingForm({...bookingForm, date: e.target.value})}
                      min={new Date().toISOString().split('T')[0]}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Time *
                    </label>
                    <select
                      required
                      value={bookingForm.time}
                      onChange={(e) => setBookingForm({...bookingForm, time: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="">Select time</option>
                      <option value="09:00">9:00 AM</option>
                      <option value="10:00">10:00 AM</option>
                      <option value="11:00">11:00 AM</option>
                      <option value="14:00">2:00 PM</option>
                      <option value="15:00">3:00 PM</option>
                      <option value="16:00">4:00 PM</option>
                      <option value="17:00">5:00 PM</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Session Type *
                  </label>
                  <select
                    required
                    value={bookingForm.sessionType}
                    onChange={(e) => setBookingForm({...bookingForm, sessionType: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  >
                    <option value="">Select session type</option>
                    <option value="Strategy Review">Strategy Review</option>
                    <option value="Risk Assessment">Risk Assessment</option>
                    <option value="Technical Analysis">Technical Analysis</option>
                    <option value="Portfolio Review">Portfolio Review</option>
                    <option value="Trading Psychology">Trading Psychology</option>
                    <option value="General Consultation">General Consultation</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Duration *
                  </label>
                  <select
                    required
                    value={bookingForm.duration}
                    onChange={(e) => setBookingForm({...bookingForm, duration: parseInt(e.target.value)})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  >
                    <option value={30}>30 minutes (${selectedCoach.hourlyRate * 0.5})</option>
                    <option value={45}>45 minutes (${selectedCoach.hourlyRate * 0.75})</option>
                    <option value={60}>60 minutes (${selectedCoach.hourlyRate})</option>
                    <option value={90}>90 minutes (${selectedCoach.hourlyRate * 1.5})</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Notes (Optional)
                  </label>
                  <textarea
                    value={bookingForm.notes}
                    onChange={(e) => setBookingForm({...bookingForm, notes: e.target.value})}
                    rows={3}
                    placeholder="Any specific topics you'd like to discuss..."
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>

                {/* Terms and Conditions */}
                <div className="flex items-start space-x-2">
                  <input
                    type="checkbox"
                    id="bookingTerms"
                    required
                    checked={bookingForm.agreeToTerms}
                    onChange={(e) => setBookingForm({...bookingForm, agreeToTerms: e.target.checked})}
                    className="mt-1 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
                  />
                  <label htmlFor="bookingTerms" className="text-sm text-gray-700 dark:text-gray-300">
                    I agree to the{' '}
                    <a href="#" className="text-primary-600 hover:text-primary-700 underline">
                      Terms of Service
                    </a>{' '}
                    and understand the cancellation policy. Payment will be processed upon booking confirmation. *
                  </label>
                </div>

                {/* Total Cost */}
                <div className="bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-800 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <span className="font-medium text-primary-900 dark:text-primary-100">Total Cost:</span>
                    <span className="text-xl font-bold text-primary-600 dark:text-primary-400">
                      ${selectedCoach.hourlyRate * (bookingForm.duration / 60)}
                    </span>
                  </div>
                </div>

                {/* Submit Button */}
                <button
                  type="submit"
                  disabled={!bookingForm.agreeToTerms}
                  className="w-full bg-primary-600 text-white py-3 px-6 rounded-lg hover:bg-primary-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors font-medium"
                >
                  Book Session
                </button>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Payment Modal */}
      {showPaymentModal && selectedCourseForPayment && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              {/* Modal Header */}
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                  Complete Payment
                </h3>
                <button
                  onClick={closePaymentModal}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {paymentSuccess ? (
                /* Success State */
                <div className="text-center py-8">
                  <div className="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8 text-green-600 dark:text-green-400" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                  </div>
                  <h4 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">Payment Successful!</h4>
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    You have successfully enrolled in <strong>{selectedCourseForPayment.title}</strong>
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-500">
                    You can now access all course materials and start learning.
                  </p>
                </div>
              ) : (
                /* Payment Form */
                <>
                  {/* Course Info */}
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
                    <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                      {selectedCourseForPayment.title}
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                      {selectedCourseForPayment.description}
                    </p>
                    <div className="flex items-center justify-between">
                      <span className="text-2xl font-bold text-primary-600 dark:text-primary-400">
                        KSh {(selectedCourseForPayment.price * 130).toLocaleString()}
                      </span>
                      <span className="text-sm text-gray-500 dark:text-gray-500">
                        (~${selectedCourseForPayment.price} USD)
                      </span>
                    </div>
                  </div>

                  {/* M-Pesa Info */}
                  <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-6">
                    <div className="flex items-center space-x-3 mb-2">
                      <div className="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center">
                        <span className="text-white font-bold text-sm">M</span>
                      </div>
                      <h5 className="font-semibold text-green-900 dark:text-green-100">M-Pesa Payment</h5>
                    </div>
                    <p className="text-sm text-green-800 dark:text-green-200">
                      You will receive an M-Pesa prompt on your phone to complete the payment.
                    </p>
                  </div>

                  {/* Payment Form */}
                  <form onSubmit={handlePaymentSubmit} className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Full Name *
                      </label>
                      <input
                        type="text"
                        required
                        value={paymentForm.name}
                        onChange={(e) => setPaymentForm({...paymentForm, name: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        placeholder="Enter your full name"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Email Address *
                      </label>
                      <input
                        type="email"
                        required
                        value={paymentForm.email}
                        onChange={(e) => setPaymentForm({...paymentForm, email: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        placeholder="Enter your email address"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        M-Pesa Phone Number *
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <span className="text-gray-500 dark:text-gray-400 text-sm">+254</span>
                        </div>
                        <input
                          type="tel"
                          required
                          value={paymentForm.phone}
                          onChange={(e) => setPaymentForm({...paymentForm, phone: e.target.value})}
                          className="w-full pl-12 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          placeholder="7XXXXXXXX"
                          pattern="[7][0-9]{8}"
                          title="Please enter a valid Kenyan phone number starting with 7"
                        />
                      </div>
                      <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                        Enter your phone number without the country code (e.g., 712345678)
                      </p>
                    </div>

                    {/* Terms and Conditions */}
                    <div className="flex items-start space-x-2">
                      <input
                        type="checkbox"
                        id="paymentTerms"
                        required
                        checked={paymentForm.agreeToTerms}
                        onChange={(e) => setPaymentForm({...paymentForm, agreeToTerms: e.target.checked})}
                        className="mt-1 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
                      />
                      <label htmlFor="paymentTerms" className="text-sm text-gray-700 dark:text-gray-300">
                        I agree to the{' '}
                        <a href="#" className="text-primary-600 hover:text-primary-700 underline">
                          Terms of Service
                        </a>{' '}
                        and{' '}
                        <a href="#" className="text-primary-600 hover:text-primary-700 underline">
                          Privacy Policy
                        </a>
                        . I understand that payment will be processed through M-Pesa. *
                      </label>
                    </div>

                    {/* Error Message */}
                    {paymentError && (
                      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3">
                        <p className="text-sm text-red-800 dark:text-red-200">{paymentError}</p>
                      </div>
                    )}

                    {/* Submit Button */}
                    <button
                      type="submit"
                      disabled={paymentLoading || !paymentForm.agreeToTerms}
                      className="w-full bg-green-600 text-white py-3 px-6 rounded-lg hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors font-medium flex items-center justify-center space-x-2"
                    >
                      {paymentLoading ? (
                        <>
                          <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          <span>Processing Payment...</span>
                        </>
                      ) : (
                        <>
                          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                          </svg>
                          <span>Pay with M-Pesa</span>
                        </>
                      )}
                    </button>
                  </form>

                  {/* Payment Info */}
                  <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                    <h6 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">Payment Process:</h6>
                    <ol className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                      <li>1. Click "Pay with M-Pesa" button</li>
                      <li>2. Check your phone for M-Pesa prompt</li>
                      <li>3. Enter your M-Pesa PIN to complete payment</li>
                      <li>4. You'll receive confirmation and course access</li>
                    </ol>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Dashboard;
