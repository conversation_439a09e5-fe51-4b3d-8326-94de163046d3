import React from 'react';
import { useScrollAnimation } from '../hooks/useScrollAnimation';

const Hero: React.FC = () => {
  const { ref: heroRef, isVisible: heroVisible } = useScrollAnimation();
  const { ref: statsRef, isVisible: statsVisible } = useScrollAnimation({ threshold: 0.2 });

  return (
    <section id="home" className="bg-gradient-to-br from-primary-50 to-primary-100 dark:from-gray-900 dark:to-gray-800 pt-24 pb-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div
          ref={heroRef}
          className={`text-center transition-all duration-1000 ${
            heroVisible
              ? 'opacity-100 translate-y-0'
              : 'opacity-0 translate-y-10'
          }`}
        >
          {/* Logo and Brand */}
          <div className="flex justify-center mb-8">
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 bg-primary-600 rounded-xl flex items-center justify-center">
                <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M3 13h8V3H9v6H3v4zm0 8h6v-2H3v2zm10 0h8v-6h-8v6zm5-12V3h-5v6h5z"/>
                </svg>
              </div>
              <div className="text-left">
                <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white">
                  E-TRIB
                </h1>
                <p className="text-xl md:text-2xl text-primary-600 font-semibold">
                  CONCEPTS
                </p>
              </div>
            </div>
          </div>

          {/* Tagline */}
          <h2 className="text-2xl md:text-3xl font-semibold text-gray-700 dark:text-gray-300 mb-6">
            Your Path to Financial Freedom
          </h2>

          {/* Description */}
          <p className="text-lg text-gray-600 dark:text-gray-400 mb-8 max-w-3xl mx-auto">
            Master the art of forex trading with our comprehensive educational platform. 
            Get real-time signals, expert guidance, and personalized coaching to transform 
            your trading journey.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="btn-primary text-lg px-8 py-3">
              Start Learning Today
            </button>
            <button className="btn-secondary text-lg px-8 py-3">
              View Our Signals
            </button>
          </div>

          {/* Stats */}
          <div
            ref={statsRef}
            className={`mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 transition-all duration-1000 delay-300 ${
              statsVisible
                ? 'opacity-100 translate-y-0'
                : 'opacity-0 translate-y-10'
            }`}
          >
            <div className="text-center">
              <div className="text-3xl font-bold text-primary-600 mb-2">5000+</div>
              <div className="text-gray-600 dark:text-gray-400">Active Traders</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary-600 mb-2">95%</div>
              <div className="text-gray-600 dark:text-gray-400">Success Rate</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary-600 mb-2">24/7</div>
              <div className="text-gray-600 dark:text-gray-400">Support</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
