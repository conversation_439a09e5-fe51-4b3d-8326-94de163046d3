import React from 'react';
import { useScrollAnimation } from '../hooks/useScrollAnimation';

const Hero: React.FC = () => {
  const { ref: heroRef, isVisible: heroVisible } = useScrollAnimation();
  const { ref: statsRef, isVisible: statsVisible } = useScrollAnimation({ threshold: 0.2 });
  const { ref: phoneRef, isVisible: phoneVisible } = useScrollAnimation();

  return (
    <section id="home" className="bg-gradient-to-br from-primary-50 to-primary-100 dark:from-gray-900 dark:to-gray-800 pt-24 pb-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left Column - Content */}
          <div
            ref={heroRef}
            className={`transition-all duration-1000 ${
              heroVisible
                ? 'opacity-100 translate-y-0'
                : 'opacity-0 translate-y-10'
            }`}
          >
            {/* Logo and Brand */}
            <div className="flex justify-center lg:justify-start mb-8">
              <div className="flex items-center space-x-4">
                <div className="w-16 h-16 bg-primary-600 rounded-xl flex items-center justify-center">
                  <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M3 13h8V3H9v6H3v4zm0 8h6v-2H3v2zm10 0h8v-6h-8v6zm5-12V3h-5v6h5z"/>
                  </svg>
                </div>
                <div className="text-left">
                  <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white">
                    E-TRIB
                  </h1>
                  <p className="text-xl md:text-2xl text-primary-600 font-semibold">
                    CONCEPTS
                  </p>
                </div>
              </div>
            </div>

            {/* Tagline */}
            <h2 className="text-2xl md:text-3xl font-semibold text-gray-700 dark:text-gray-300 mb-6 text-center lg:text-left">
              Your Path to Financial Freedom
            </h2>

            {/* Description */}
            <p className="text-lg text-gray-600 dark:text-gray-400 mb-8 text-center lg:text-left">
              Master the art of forex trading with our comprehensive educational platform.
              Get real-time signals, expert guidance, and personalized coaching to transform
              your trading journey.
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start mb-12">
              <button className="btn-primary text-lg px-8 py-3">
                Start Learning Today
              </button>
              <button className="btn-secondary text-lg px-8 py-3">
                View Our Signals
              </button>
            </div>

            {/* Stats */}
            <div
              ref={statsRef}
              className={`grid grid-cols-1 md:grid-cols-3 gap-8 transition-all duration-1000 delay-300 ${
                statsVisible
                  ? 'opacity-100 translate-y-0'
                  : 'opacity-0 translate-y-10'
              }`}
            >
              <div className="text-center lg:text-left">
                <div className="text-3xl font-bold text-primary-600 mb-2">5000+</div>
                <div className="text-gray-600 dark:text-gray-400">Active Traders</div>
              </div>
              <div className="text-center lg:text-left">
                <div className="text-3xl font-bold text-primary-600 mb-2">95%</div>
                <div className="text-gray-600 dark:text-gray-400">Success Rate</div>
              </div>
              <div className="text-center lg:text-left">
                <div className="text-3xl font-bold text-primary-600 mb-2">24/7</div>
                <div className="text-gray-600 dark:text-gray-400">Support</div>
              </div>
            </div>
          </div>

          {/* Right Column - Phone Mockup */}
          <div
            ref={phoneRef}
            className={`flex justify-center lg:justify-end transition-all duration-1000 delay-500 ${
              phoneVisible
                ? 'opacity-100 translate-x-0'
                : 'opacity-0 translate-x-10'
            }`}
          >
            {/* iPhone 14 Pro Mockup */}
            <div className="relative">
              {/* Phone Frame */}
              <div className="relative w-80 h-[640px] bg-black rounded-[3rem] p-2 shadow-2xl">
                {/* Screen */}
                <div className="w-full h-full bg-white dark:bg-gray-900 rounded-[2.5rem] overflow-hidden relative">
                  {/* Dynamic Island */}
                  <div className="absolute top-2 left-1/2 transform -translate-x-1/2 w-32 h-6 bg-black rounded-full z-10"></div>

                  {/* Dashboard Content */}
                  <div className="h-full flex flex-col">
                    {/* Mobile Header */}
                    <div className="bg-white dark:bg-gray-800 px-4 py-3 pt-8 border-b border-gray-200 dark:border-gray-700">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                            <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                              <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
                            </svg>
                          </div>
                          <h1 className="text-lg font-bold text-primary-600">E-TRIBE</h1>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                            <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                              <path d="M3 13h8V3H9v6H3v4zm0 8h6v-2H3v2zm10 0h8v-6h-8v6zm5-12V3h-5v6h5z"/>
                            </svg>
                          </div>
                          <button className="p-2 rounded-lg border border-gray-300">
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                            </svg>
                          </button>
                        </div>
                      </div>
                    </div>

                    {/* Content Area */}
                    <div className="flex-1 p-4 bg-gray-50 dark:bg-gray-900 overflow-hidden">
                      {/* Stats Cards */}
                      <div className="grid grid-cols-2 gap-3 mb-4">
                        <div className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm">
                          <div className="text-xs text-gray-600 dark:text-gray-400">Total Profit</div>
                          <div className="text-lg font-bold text-green-600">$2,450</div>
                        </div>
                        <div className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm">
                          <div className="text-xs text-gray-600 dark:text-gray-400">Active Signals</div>
                          <div className="text-lg font-bold text-blue-600">12</div>
                        </div>
                      </div>

                      {/* Recent Signals */}
                      <div className="bg-white dark:bg-gray-800 rounded-lg p-4 mb-4">
                        <h3 className="text-sm font-semibold text-gray-900 dark:text-white mb-3">Recent Signals</h3>
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                              <span className="text-sm text-gray-900 dark:text-white">EUR/USD</span>
                            </div>
                            <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">BUY</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                              <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                              <span className="text-sm text-gray-900 dark:text-white">GBP/JPY</span>
                            </div>
                            <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded">SELL</span>
                          </div>
                        </div>
                      </div>

                      {/* Course Progress */}
                      <div className="bg-white dark:bg-gray-800 rounded-lg p-4">
                        <h3 className="text-sm font-semibold text-gray-900 dark:text-white mb-3">Course Progress</h3>
                        <div className="space-y-3">
                          <div>
                            <div className="flex justify-between text-xs mb-1">
                              <span className="text-gray-600 dark:text-gray-400">Forex Fundamentals</span>
                              <span className="text-blue-600">75%</span>
                            </div>
                            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                              <div className="bg-blue-600 h-2 rounded-full" style={{width: '75%'}}></div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Bottom Navigation */}
                    <div className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 px-4 py-2">
                      <div className="flex justify-around">
                        <button className="flex flex-col items-center py-2">
                          <svg className="w-5 h-5 text-primary-600" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
                          </svg>
                          <span className="text-xs text-primary-600 mt-1 font-semibold">E-TRIBE</span>
                        </button>
                        <button className="flex flex-col items-center py-2">
                          <svg className="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z"/>
                          </svg>
                          <span className="text-xs text-gray-400 mt-1">Signals</span>
                        </button>
                        <button className="flex flex-col items-center py-2">
                          <svg className="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
                          </svg>
                          <span className="text-xs text-gray-400 mt-1">Courses</span>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Floating Elements */}
              <div className="absolute -right-8 top-20 bg-green-500 text-white px-3 py-2 rounded-lg shadow-lg text-sm animate-pulse">
                +$125 Profit!
              </div>
              <div className="absolute -left-8 bottom-32 bg-blue-500 text-white px-3 py-2 rounded-lg shadow-lg text-sm animate-bounce">
                New Signal
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
